多星座模式训练实验
================================================================================
实验时间: 2025_08_13_15_04_48
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-13 15:11:19
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 704.217, reward: 12.801, critic_reward: 16.630, revenue_rate: 0.3215, distance: 4.6473, memory: -0.0974, power: 0.1399, lr: 0.000100, took: 45.787s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 41.482, reward: 17.886, critic_reward: 17.342, revenue_rate: 0.4471, distance: 6.2890, memory: -0.1414, power: 0.1904, lr: 0.000100, took: 53.402s
[COOPERATIVE] Epoch 1, Batch 30/1563, loss: 12.732, reward: 20.117, critic_reward: 20.841, revenue_rate: 0.5010, distance: 6.9045, memory: -0.1132, power: 0.2082, lr: 0.000100, took: 56.331s
[COOPERATIVE] Epoch 1, Batch 40/1563, loss: 12.447, reward: 24.419, critic_reward: 24.849, revenue_rate: 0.6104, distance: 8.2402, memory: -0.0895, power: 0.2478, lr: 0.000100, took: 66.460s
[COOPERATIVE] Epoch 1, Batch 50/1563, loss: 13.623, reward: 27.552, critic_reward: 27.729, revenue_rate: 0.6862, distance: 9.1284, memory: -0.0556, power: 0.2775, lr: 0.000100, took: 74.503s
[COOPERATIVE] Epoch 1, Batch 60/1563, loss: 9.068, reward: 26.612, critic_reward: 27.193, revenue_rate: 0.6594, distance: 8.6424, memory: -0.0683, power: 0.2592, lr: 0.000100, took: 70.018s
[COOPERATIVE] Epoch 1, Batch 70/1563, loss: 7.218, reward: 23.221, critic_reward: 22.993, revenue_rate: 0.5708, distance: 7.1532, memory: -0.1086, power: 0.2155, lr: 0.000100, took: 59.950s
[COOPERATIVE] Epoch 1, Batch 80/1563, loss: 6.382, reward: 25.632, critic_reward: 25.455, revenue_rate: 0.6312, distance: 8.0721, memory: -0.0883, power: 0.2427, lr: 0.000100, took: 65.403s
[COOPERATIVE] Epoch 1, Batch 90/1563, loss: 8.338, reward: 27.420, critic_reward: 26.738, revenue_rate: 0.6808, distance: 8.9909, memory: -0.0597, power: 0.2727, lr: 0.000100, took: 73.227s
[COOPERATIVE] Epoch 1, Batch 100/1563, loss: 4.338, reward: 28.379, critic_reward: 28.411, revenue_rate: 0.7031, distance: 9.0955, memory: -0.0561, power: 0.2725, lr: 0.000100, took: 74.557s
[COOPERATIVE] Epoch 1, Batch 110/1563, loss: 5.002, reward: 27.720, critic_reward: 28.076, revenue_rate: 0.6838, distance: 8.6555, memory: -0.0701, power: 0.2600, lr: 0.000100, took: 75.345s
[COOPERATIVE] Epoch 1, Batch 120/1563, loss: 7.275, reward: 27.700, critic_reward: 27.948, revenue_rate: 0.6810, distance: 8.5290, memory: -0.0724, power: 0.2600, lr: 0.000100, took: 72.802s
[COOPERATIVE] Epoch 1, Batch 130/1563, loss: 6.419, reward: 26.169, critic_reward: 25.030, revenue_rate: 0.6448, distance: 8.1055, memory: -0.0848, power: 0.2430, lr: 0.000100, took: 65.664s
[COOPERATIVE] Epoch 1, Batch 140/1563, loss: 5.465, reward: 28.696, critic_reward: 28.659, revenue_rate: 0.7091, distance: 9.0496, memory: -0.0502, power: 0.2736, lr: 0.000100, took: 72.333s
[COOPERATIVE] Epoch 1, Batch 150/1563, loss: 8.412, reward: 28.189, critic_reward: 27.409, revenue_rate: 0.7002, distance: 8.9336, memory: -0.0563, power: 0.2695, lr: 0.000100, took: 73.038s
[COOPERATIVE] Epoch 1, Batch 160/1563, loss: 7.693, reward: 28.288, critic_reward: 29.458, revenue_rate: 0.6959, distance: 8.6898, memory: -0.0625, power: 0.2649, lr: 0.000100, took: 73.103s
[COOPERATIVE] Epoch 1, Batch 170/1563, loss: 4.754, reward: 30.564, critic_reward: 30.243, revenue_rate: 0.7545, distance: 9.5059, memory: -0.0395, power: 0.2856, lr: 0.000100, took: 79.632s
[COOPERATIVE] Epoch 1, Batch 180/1563, loss: 3.741, reward: 29.758, critic_reward: 29.938, revenue_rate: 0.7334, distance: 9.1440, memory: -0.0487, power: 0.2756, lr: 0.000100, took: 79.679s
[COOPERATIVE] Epoch 1, Batch 190/1563, loss: 4.147, reward: 28.365, critic_reward: 28.196, revenue_rate: 0.6941, distance: 8.5222, memory: -0.0744, power: 0.2563, lr: 0.000100, took: 74.198s
[COOPERATIVE] Epoch 1, Batch 200/1563, loss: 5.605, reward: 29.307, critic_reward: 29.895, revenue_rate: 0.7206, distance: 8.9256, memory: -0.0604, power: 0.2692, lr: 0.000100, took: 74.610s
[COOPERATIVE] Epoch 1, Batch 210/1563, loss: 9.004, reward: 30.930, critic_reward: 31.190, revenue_rate: 0.7623, distance: 9.6180, memory: -0.0432, power: 0.2893, lr: 0.000100, took: 83.668s
[COOPERATIVE] Epoch 1, Batch 220/1563, loss: 17.512, reward: 30.616, critic_reward: 30.343, revenue_rate: 0.7546, distance: 9.5228, memory: -0.0368, power: 0.2870, lr: 0.000100, took: 85.954s
[COOPERATIVE] Epoch 1, Batch 230/1563, loss: 8.259, reward: 30.617, critic_reward: 29.992, revenue_rate: 0.7565, distance: 9.4514, memory: -0.0417, power: 0.2876, lr: 0.000100, took: 84.687s
[COOPERATIVE] Epoch 1, Batch 240/1563, loss: 3.429, reward: 30.240, critic_reward: 30.519, revenue_rate: 0.7442, distance: 9.3413, memory: -0.0421, power: 0.2810, lr: 0.000100, took: 80.240s
[COOPERATIVE] Epoch 1, Batch 250/1563, loss: 4.904, reward: 29.569, critic_reward: 29.917, revenue_rate: 0.7263, distance: 8.9125, memory: -0.0572, power: 0.2708, lr: 0.000100, took: 76.837s
[COOPERATIVE] Epoch 1, Batch 260/1563, loss: 17.512, reward: 27.830, critic_reward: 27.191, revenue_rate: 0.6816, distance: 8.3606, memory: -0.0716, power: 0.2515, lr: 0.000100, took: 69.080s
[COOPERATIVE] Epoch 1, Batch 270/1563, loss: 21.293, reward: 29.844, critic_reward: 28.916, revenue_rate: 0.7316, distance: 9.0850, memory: -0.0599, power: 0.2731, lr: 0.000100, took: 78.111s
[COOPERATIVE] Epoch 1, Batch 280/1563, loss: 11.233, reward: 30.510, critic_reward: 30.505, revenue_rate: 0.7523, distance: 9.6610, memory: -0.0318, power: 0.2918, lr: 0.000100, took: 81.170s
[COOPERATIVE] Epoch 1, Batch 290/1563, loss: 7.388, reward: 31.293, critic_reward: 31.553, revenue_rate: 0.7759, distance: 10.0688, memory: -0.0192, power: 0.3064, lr: 0.000100, took: 85.214s
[COOPERATIVE] Epoch 1, Batch 300/1563, loss: 8.451, reward: 27.842, critic_reward: 28.679, revenue_rate: 0.6841, distance: 8.7378, memory: -0.0482, power: 0.2652, lr: 0.000100, took: 74.799s
[COOPERATIVE] Epoch 1, Batch 310/1563, loss: 4.790, reward: 26.794, critic_reward: 26.040, revenue_rate: 0.6577, distance: 8.1495, memory: -0.0737, power: 0.2470, lr: 0.000100, took: 72.526s
[COOPERATIVE] Epoch 1, Batch 320/1563, loss: 3.728, reward: 30.572, critic_reward: 30.436, revenue_rate: 0.7533, distance: 9.4814, memory: -0.0346, power: 0.2878, lr: 0.000100, took: 81.836s
[COOPERATIVE] Epoch 1, Batch 330/1563, loss: 6.649, reward: 31.841, critic_reward: 30.786, revenue_rate: 0.7894, distance: 9.9536, memory: -0.0289, power: 0.3017, lr: 0.000100, took: 86.714s
[COOPERATIVE] Epoch 1, Batch 340/1563, loss: 6.823, reward: 30.251, critic_reward: 30.057, revenue_rate: 0.7451, distance: 9.2058, memory: -0.0385, power: 0.2817, lr: 0.000100, took: 78.535s
[COOPERATIVE] Epoch 1, Batch 350/1563, loss: 10.389, reward: 31.369, critic_reward: 31.344, revenue_rate: 0.7736, distance: 9.8367, memory: -0.0295, power: 0.2980, lr: 0.000100, took: 83.181s
[COOPERATIVE] Epoch 1, Batch 360/1563, loss: 18.670, reward: 32.465, critic_reward: 34.114, revenue_rate: 0.8036, distance: 10.3565, memory: -0.0130, power: 0.3139, lr: 0.000100, took: 91.050s
[COOPERATIVE] Epoch 1, Batch 370/1563, loss: 7.678, reward: 29.744, critic_reward: 30.017, revenue_rate: 0.7350, distance: 9.2870, memory: -0.0447, power: 0.2811, lr: 0.000100, took: 82.460s
[COOPERATIVE] Epoch 1, Batch 380/1563, loss: 5.876, reward: 31.368, critic_reward: 32.194, revenue_rate: 0.7715, distance: 9.6439, memory: -0.0382, power: 0.2911, lr: 0.000100, took: 82.405s
[COOPERATIVE] Epoch 1, Batch 390/1563, loss: 4.833, reward: 28.671, critic_reward: 28.358, revenue_rate: 0.7057, distance: 8.8325, memory: -0.0634, power: 0.2687, lr: 0.000100, took: 75.582s
[COOPERATIVE] Epoch 1, Batch 400/1563, loss: 3.797, reward: 30.367, critic_reward: 30.255, revenue_rate: 0.7481, distance: 9.4796, memory: -0.0426, power: 0.2857, lr: 0.000100, took: 75.750s
[COOPERATIVE] Epoch 1, Batch 410/1563, loss: 4.119, reward: 29.930, critic_reward: 30.019, revenue_rate: 0.7339, distance: 9.1228, memory: -0.0532, power: 0.2750, lr: 0.000100, took: 71.675s
[COOPERATIVE] Epoch 1, Batch 420/1563, loss: 11.076, reward: 30.196, critic_reward: 30.310, revenue_rate: 0.7418, distance: 9.2298, memory: -0.0458, power: 0.2798, lr: 0.000100, took: 74.500s
[COOPERATIVE] Epoch 1, Batch 430/1563, loss: 8.883, reward: 30.455, critic_reward: 31.394, revenue_rate: 0.7455, distance: 9.2081, memory: -0.0508, power: 0.2807, lr: 0.000100, took: 72.835s
[COOPERATIVE] Epoch 1, Batch 440/1563, loss: 7.682, reward: 29.860, critic_reward: 30.228, revenue_rate: 0.7343, distance: 9.0391, memory: -0.0532, power: 0.2732, lr: 0.000100, took: 73.959s
[COOPERATIVE] Epoch 1, Batch 450/1563, loss: 8.335, reward: 30.063, critic_reward: 30.916, revenue_rate: 0.7411, distance: 9.2253, memory: -0.0557, power: 0.2804, lr: 0.000100, took: 72.724s
[COOPERATIVE] Epoch 1, Batch 460/1563, loss: 3.627, reward: 29.688, critic_reward: 29.623, revenue_rate: 0.7294, distance: 9.0873, memory: -0.0525, power: 0.2748, lr: 0.000100, took: 71.531s
[COOPERATIVE] Epoch 1, Batch 470/1563, loss: 5.653, reward: 31.363, critic_reward: 31.780, revenue_rate: 0.7707, distance: 9.6329, memory: -0.0318, power: 0.2924, lr: 0.000100, took: 76.434s
[COOPERATIVE] Epoch 1, Batch 480/1563, loss: 6.275, reward: 29.488, critic_reward: 28.732, revenue_rate: 0.7261, distance: 9.1024, memory: -0.0468, power: 0.2750, lr: 0.000100, took: 71.839s
[COOPERATIVE] Epoch 1, Batch 490/1563, loss: 4.177, reward: 31.740, critic_reward: 32.068, revenue_rate: 0.7864, distance: 10.2576, memory: -0.0154, power: 0.3086, lr: 0.000100, took: 80.477s
[COOPERATIVE] Epoch 1, Batch 500/1563, loss: 4.021, reward: 31.983, critic_reward: 31.717, revenue_rate: 0.7900, distance: 10.0812, memory: -0.0267, power: 0.3072, lr: 0.000100, took: 82.030s
[COOPERATIVE] Epoch 1, Batch 510/1563, loss: 3.852, reward: 30.919, critic_reward: 31.155, revenue_rate: 0.7622, distance: 9.5264, memory: -0.0365, power: 0.2895, lr: 0.000100, took: 80.573s
[COOPERATIVE] Epoch 1, Batch 520/1563, loss: 3.733, reward: 26.726, critic_reward: 26.238, revenue_rate: 0.6529, distance: 7.8544, memory: -0.0862, power: 0.2392, lr: 0.000100, took: 66.671s
[COOPERATIVE] Epoch 1, Batch 530/1563, loss: 3.539, reward: 29.928, critic_reward: 30.259, revenue_rate: 0.7342, distance: 9.0141, memory: -0.0446, power: 0.2743, lr: 0.000100, took: 79.065s
[COOPERATIVE] Epoch 1, Batch 540/1563, loss: 7.062, reward: 30.404, critic_reward: 31.475, revenue_rate: 0.7454, distance: 9.1247, memory: -0.0502, power: 0.2763, lr: 0.000100, took: 79.024s
[COOPERATIVE] Epoch 1, Batch 550/1563, loss: 6.836, reward: 29.420, critic_reward: 30.393, revenue_rate: 0.7202, distance: 8.7896, memory: -0.0596, power: 0.2645, lr: 0.000100, took: 74.188s
[COOPERATIVE] Epoch 1, Batch 560/1563, loss: 6.884, reward: 30.867, critic_reward: 31.220, revenue_rate: 0.7586, distance: 9.3316, memory: -0.0501, power: 0.2839, lr: 0.000100, took: 81.502s
[COOPERATIVE] Epoch 1, Batch 570/1563, loss: 4.475, reward: 30.796, critic_reward: 30.545, revenue_rate: 0.7543, distance: 9.2365, memory: -0.0457, power: 0.2807, lr: 0.000100, took: 76.974s
[COOPERATIVE] Epoch 1, Batch 580/1563, loss: 14.886, reward: 31.545, critic_reward: 32.229, revenue_rate: 0.7772, distance: 9.7756, memory: -0.0273, power: 0.2949, lr: 0.000100, took: 84.233s
[COOPERATIVE] Epoch 1, Batch 590/1563, loss: 19.990, reward: 30.778, critic_reward: 30.090, revenue_rate: 0.7564, distance: 9.3079, memory: -0.0464, power: 0.2814, lr: 0.000100, took: 78.942s
[COOPERATIVE] Epoch 1, Batch 600/1563, loss: 6.645, reward: 30.878, critic_reward: 31.090, revenue_rate: 0.7543, distance: 9.2806, memory: -0.0439, power: 0.2822, lr: 0.000100, took: 78.769s
[COOPERATIVE] Epoch 1, Batch 610/1563, loss: 5.679, reward: 30.766, critic_reward: 30.942, revenue_rate: 0.7547, distance: 9.2727, memory: -0.0312, power: 0.2852, lr: 0.000100, took: 79.635s
[COOPERATIVE] Epoch 1, Batch 620/1563, loss: 5.622, reward: 31.071, critic_reward: 29.941, revenue_rate: 0.7630, distance: 9.3938, memory: -0.0440, power: 0.2833, lr: 0.000100, took: 79.406s
[COOPERATIVE] Epoch 1, Batch 630/1563, loss: 10.542, reward: 30.971, critic_reward: 32.543, revenue_rate: 0.7583, distance: 9.2199, memory: -0.0527, power: 0.2802, lr: 0.000100, took: 77.872s
[COOPERATIVE] Epoch 1, Batch 640/1563, loss: 3.293, reward: 29.871, critic_reward: 29.504, revenue_rate: 0.7324, distance: 8.8983, memory: -0.0503, power: 0.2693, lr: 0.000100, took: 75.013s
[COOPERATIVE] Epoch 1, Batch 650/1563, loss: 3.686, reward: 30.927, critic_reward: 31.545, revenue_rate: 0.7626, distance: 9.3864, memory: -0.0475, power: 0.2815, lr: 0.000100, took: 80.967s
[COOPERATIVE] Epoch 1, Batch 660/1563, loss: 3.739, reward: 30.231, critic_reward: 30.207, revenue_rate: 0.7383, distance: 8.8780, memory: -0.0628, power: 0.2702, lr: 0.000100, took: 74.953s
[COOPERATIVE] Epoch 1, Batch 670/1563, loss: 4.733, reward: 29.013, critic_reward: 28.875, revenue_rate: 0.7113, distance: 8.7121, memory: -0.0603, power: 0.2644, lr: 0.000100, took: 73.154s
[COOPERATIVE] Epoch 1, Batch 680/1563, loss: 4.430, reward: 29.172, critic_reward: 29.107, revenue_rate: 0.7157, distance: 8.6681, memory: -0.0611, power: 0.2627, lr: 0.000100, took: 75.436s
[COOPERATIVE] Epoch 1, Batch 690/1563, loss: 6.057, reward: 29.978, critic_reward: 28.970, revenue_rate: 0.7383, distance: 9.1458, memory: -0.0083, power: 0.2780, lr: 0.000100, took: 77.391s
[COOPERATIVE] Epoch 1, Batch 700/1563, loss: 10.838, reward: 30.483, critic_reward: 30.985, revenue_rate: 0.7466, distance: 9.0581, memory: -0.0519, power: 0.2759, lr: 0.000100, took: 76.535s
[COOPERATIVE] Epoch 1, Batch 710/1563, loss: 8.251, reward: 30.495, critic_reward: 31.958, revenue_rate: 0.7490, distance: 9.1365, memory: -0.0454, power: 0.2758, lr: 0.000100, took: 77.150s
[COOPERATIVE] Epoch 1, Batch 720/1563, loss: 5.225, reward: 30.325, critic_reward: 29.853, revenue_rate: 0.7439, distance: 8.9608, memory: -0.0604, power: 0.2720, lr: 0.000100, took: 75.432s
[COOPERATIVE] Epoch 1, Batch 730/1563, loss: 4.525, reward: 29.549, critic_reward: 30.133, revenue_rate: 0.7200, distance: 8.6082, memory: -0.0715, power: 0.2638, lr: 0.000100, took: 72.885s
[COOPERATIVE] Epoch 1, Batch 740/1563, loss: 6.232, reward: 28.885, critic_reward: 28.869, revenue_rate: 0.7064, distance: 8.4894, memory: -0.0733, power: 0.2552, lr: 0.000100, took: 71.431s
[COOPERATIVE] Epoch 1, Batch 750/1563, loss: 4.349, reward: 31.488, critic_reward: 31.564, revenue_rate: 0.7721, distance: 9.5411, memory: -0.0288, power: 0.2932, lr: 0.000100, took: 81.629s
[COOPERATIVE] Epoch 1, Batch 760/1563, loss: 12.415, reward: 33.421, critic_reward: 35.319, revenue_rate: 0.8276, distance: 10.6269, memory: 0.0025, power: 0.3202, lr: 0.000100, took: 91.234s
[COOPERATIVE] Epoch 1, Batch 770/1563, loss: 3.540, reward: 30.436, critic_reward: 29.984, revenue_rate: 0.7466, distance: 9.0743, memory: -0.0442, power: 0.2755, lr: 0.000100, took: 78.841s
[COOPERATIVE] Epoch 1, Batch 780/1563, loss: 3.185, reward: 29.843, critic_reward: 29.803, revenue_rate: 0.7292, distance: 8.8249, memory: -0.0600, power: 0.2669, lr: 0.000100, took: 73.783s
[COOPERATIVE] Epoch 1, Batch 790/1563, loss: 3.267, reward: 28.824, critic_reward: 28.328, revenue_rate: 0.7019, distance: 8.3645, memory: -0.0670, power: 0.2545, lr: 0.000100, took: 73.783s
[COOPERATIVE] Epoch 1, Batch 800/1563, loss: 2.749, reward: 30.285, critic_reward: 30.270, revenue_rate: 0.7428, distance: 9.0729, memory: -0.0401, power: 0.2728, lr: 0.000100, took: 75.980s
[COOPERATIVE] Epoch 1, Batch 810/1563, loss: 6.226, reward: 32.201, critic_reward: 32.325, revenue_rate: 0.7905, distance: 9.9216, memory: -0.0266, power: 0.2974, lr: 0.000100, took: 84.472s
[COOPERATIVE] Epoch 1, Batch 820/1563, loss: 9.510, reward: 32.254, critic_reward: 33.451, revenue_rate: 0.7917, distance: 9.8818, memory: -0.0179, power: 0.2984, lr: 0.000100, took: 84.759s
[COOPERATIVE] Epoch 1, Batch 830/1563, loss: 3.588, reward: 32.780, critic_reward: 32.884, revenue_rate: 0.8068, distance: 10.0999, memory: -0.0184, power: 0.3053, lr: 0.000100, took: 85.757s
[COOPERATIVE] Epoch 1, Batch 840/1563, loss: 4.697, reward: 29.913, critic_reward: 29.811, revenue_rate: 0.7329, distance: 8.8518, memory: -0.0534, power: 0.2687, lr: 0.000100, took: 74.490s
[COOPERATIVE] Epoch 1, Batch 850/1563, loss: 6.644, reward: 29.544, critic_reward: 29.824, revenue_rate: 0.7264, distance: 8.8128, memory: -0.0568, power: 0.2657, lr: 0.000100, took: 73.853s
[COOPERATIVE] Epoch 1, Batch 860/1563, loss: 9.091, reward: 32.624, critic_reward: 34.570, revenue_rate: 0.8065, distance: 10.1857, memory: -0.0217, power: 0.3093, lr: 0.000100, took: 87.386s
[COOPERATIVE] Epoch 1, Batch 870/1563, loss: 3.680, reward: 32.148, critic_reward: 32.496, revenue_rate: 0.7898, distance: 9.8565, memory: -0.0258, power: 0.2954, lr: 0.000100, took: 83.518s
[COOPERATIVE] Epoch 1, Batch 880/1563, loss: 7.370, reward: 31.731, critic_reward: 31.302, revenue_rate: 0.7779, distance: 9.4338, memory: -0.0475, power: 0.2871, lr: 0.000100, took: 82.394s
[COOPERATIVE] Epoch 1, Batch 890/1563, loss: 6.713, reward: 32.094, critic_reward: 32.404, revenue_rate: 0.7865, distance: 9.6498, memory: -0.0423, power: 0.2924, lr: 0.000100, took: 82.199s
[COOPERATIVE] Epoch 1, Batch 900/1563, loss: 2.849, reward: 31.198, critic_reward: 31.285, revenue_rate: 0.7647, distance: 9.2723, memory: -0.0593, power: 0.2803, lr: 0.000100, took: 81.201s
[COOPERATIVE] Epoch 1, Batch 910/1563, loss: 3.370, reward: 27.611, critic_reward: 27.906, revenue_rate: 0.6730, distance: 7.9412, memory: -0.0845, power: 0.2392, lr: 0.000100, took: 66.820s
[COOPERATIVE] Epoch 1, Batch 920/1563, loss: 5.652, reward: 29.102, critic_reward: 30.276, revenue_rate: 0.7105, distance: 8.5372, memory: -0.0632, power: 0.2592, lr: 0.000100, took: 72.877s
[COOPERATIVE] Epoch 1, Batch 930/1563, loss: 3.296, reward: 32.453, critic_reward: 32.560, revenue_rate: 0.7971, distance: 9.8738, memory: -0.0209, power: 0.2992, lr: 0.000100, took: 84.385s
[COOPERATIVE] Epoch 1, Batch 940/1563, loss: 4.798, reward: 34.038, critic_reward: 33.646, revenue_rate: 0.8414, distance: 10.6432, memory: 0.0002, power: 0.3222, lr: 0.000100, took: 92.075s
[COOPERATIVE] Epoch 1, Batch 950/1563, loss: 3.672, reward: 32.196, critic_reward: 32.156, revenue_rate: 0.7903, distance: 9.6961, memory: -0.0384, power: 0.2959, lr: 0.000100, took: 82.854s
[COOPERATIVE] Epoch 1, Batch 960/1563, loss: 5.782, reward: 31.351, critic_reward: 32.154, revenue_rate: 0.7667, distance: 9.4365, memory: -0.0453, power: 0.2858, lr: 0.000100, took: 79.976s
[COOPERATIVE] Epoch 1, Batch 970/1563, loss: 9.234, reward: 32.554, critic_reward: 31.357, revenue_rate: 0.7988, distance: 9.8685, memory: -0.0290, power: 0.2971, lr: 0.000100, took: 84.575s
[COOPERATIVE] Epoch 1, Batch 980/1563, loss: 4.462, reward: 31.719, critic_reward: 31.540, revenue_rate: 0.7789, distance: 9.4473, memory: -0.0333, power: 0.2887, lr: 0.000100, took: 81.029s
[COOPERATIVE] Epoch 1, Batch 990/1563, loss: 5.859, reward: 30.715, critic_reward: 31.007, revenue_rate: 0.7556, distance: 9.2318, memory: -0.0472, power: 0.2789, lr: 0.000100, took: 79.691s
[COOPERATIVE] Epoch 1, Batch 1000/1563, loss: 9.895, reward: 27.940, critic_reward: 27.318, revenue_rate: 0.6824, distance: 8.1749, memory: -0.0816, power: 0.2461, lr: 0.000100, took: 67.744s
[COOPERATIVE] Epoch 1, Batch 1010/1563, loss: 6.507, reward: 29.655, critic_reward: 29.412, revenue_rate: 0.7291, distance: 8.9948, memory: -0.0288, power: 0.2735, lr: 0.000100, took: 76.246s
[COOPERATIVE] Epoch 1, Batch 1020/1563, loss: 4.231, reward: 31.906, critic_reward: 32.528, revenue_rate: 0.7869, distance: 9.8710, memory: -0.0105, power: 0.3001, lr: 0.000100, took: 86.996s
[COOPERATIVE] Epoch 1, Batch 1030/1563, loss: 4.978, reward: 31.909, critic_reward: 31.978, revenue_rate: 0.7828, distance: 9.6169, memory: -0.0344, power: 0.2905, lr: 0.000100, took: 81.524s
[COOPERATIVE] Epoch 1, Batch 1040/1563, loss: 6.053, reward: 30.996, critic_reward: 29.746, revenue_rate: 0.7592, distance: 9.2163, memory: -0.0483, power: 0.2792, lr: 0.000100, took: 77.966s
[COOPERATIVE] Epoch 1, Batch 1050/1563, loss: 6.308, reward: 30.367, critic_reward: 30.826, revenue_rate: 0.7426, distance: 8.8606, memory: -0.0509, power: 0.2730, lr: 0.000100, took: 76.165s
[COOPERATIVE] Epoch 1, Batch 1060/1563, loss: 6.236, reward: 32.006, critic_reward: 33.191, revenue_rate: 0.7832, distance: 9.5384, memory: -0.0299, power: 0.2898, lr: 0.000100, took: 81.103s
[COOPERATIVE] Epoch 1, Batch 1070/1563, loss: 3.744, reward: 32.532, critic_reward: 32.658, revenue_rate: 0.8006, distance: 10.0394, memory: -0.0189, power: 0.3038, lr: 0.000100, took: 84.990s
[COOPERATIVE] Epoch 1, Batch 1080/1563, loss: 6.533, reward: 32.991, critic_reward: 33.793, revenue_rate: 0.8085, distance: 10.1079, memory: -0.0127, power: 0.3067, lr: 0.000100, took: 86.321s
[COOPERATIVE] Epoch 1, Batch 1090/1563, loss: 6.040, reward: 32.185, critic_reward: 32.686, revenue_rate: 0.7924, distance: 9.8614, memory: -0.0253, power: 0.2972, lr: 0.000100, took: 82.740s
[COOPERATIVE] Epoch 1, Batch 1100/1563, loss: 4.289, reward: 30.967, critic_reward: 30.868, revenue_rate: 0.7559, distance: 9.1874, memory: -0.0485, power: 0.2796, lr: 0.000100, took: 76.948s
[COOPERATIVE] Epoch 1, Batch 1110/1563, loss: 5.997, reward: 29.473, critic_reward: 29.721, revenue_rate: 0.7204, distance: 8.6063, memory: -0.0713, power: 0.2605, lr: 0.000100, took: 74.195s
[COOPERATIVE] Epoch 1, Batch 1120/1563, loss: 4.416, reward: 29.634, critic_reward: 29.847, revenue_rate: 0.7201, distance: 8.6358, memory: -0.0616, power: 0.2620, lr: 0.000100, took: 72.165s
[COOPERATIVE] Epoch 1, Batch 1130/1563, loss: 4.076, reward: 30.284, critic_reward: 29.644, revenue_rate: 0.7418, distance: 9.0856, memory: -0.0474, power: 0.2743, lr: 0.000100, took: 77.595s
[COOPERATIVE] Epoch 1, Batch 1140/1563, loss: 3.997, reward: 31.424, critic_reward: 31.655, revenue_rate: 0.7703, distance: 9.4684, memory: -0.0281, power: 0.2854, lr: 0.000100, took: 79.548s
[COOPERATIVE] Epoch 1, Batch 1150/1563, loss: 4.084, reward: 29.127, critic_reward: 29.520, revenue_rate: 0.7125, distance: 8.5128, memory: -0.0667, power: 0.2593, lr: 0.000100, took: 71.351s
[COOPERATIVE] Epoch 1, Batch 1160/1563, loss: 5.824, reward: 27.126, critic_reward: 25.984, revenue_rate: 0.6659, distance: 7.9673, memory: -0.0860, power: 0.2417, lr: 0.000100, took: 65.865s
[COOPERATIVE] Epoch 1, Batch 1170/1563, loss: 3.349, reward: 33.796, critic_reward: 34.174, revenue_rate: 0.8311, distance: 10.4255, memory: -0.0090, power: 0.3157, lr: 0.000100, took: 88.797s
[COOPERATIVE] Epoch 1, Batch 1180/1563, loss: 3.623, reward: 32.978, critic_reward: 33.184, revenue_rate: 0.8164, distance: 10.1387, memory: -0.0151, power: 0.3078, lr: 0.000100, took: 86.708s
[COOPERATIVE] Epoch 1, Batch 1190/1563, loss: 4.082, reward: 32.031, critic_reward: 31.755, revenue_rate: 0.7838, distance: 9.6821, memory: -0.0382, power: 0.2936, lr: 0.000100, took: 81.185s
[COOPERATIVE] Epoch 1, Batch 1200/1563, loss: 4.052, reward: 32.175, critic_reward: 32.951, revenue_rate: 0.7904, distance: 9.7543, memory: -0.0312, power: 0.2981, lr: 0.000100, took: 82.812s
[COOPERATIVE] Epoch 1, Batch 1210/1563, loss: 2.919, reward: 31.866, critic_reward: 31.416, revenue_rate: 0.7848, distance: 9.8048, memory: -0.0320, power: 0.3008, lr: 0.000100, took: 83.450s
[COOPERATIVE] Epoch 1, Batch 1220/1563, loss: 3.551, reward: 33.897, critic_reward: 34.506, revenue_rate: 0.8389, distance: 10.7279, memory: 0.0003, power: 0.3226, lr: 0.000100, took: 93.942s
[COOPERATIVE] Epoch 1, Batch 1230/1563, loss: 3.804, reward: 32.927, critic_reward: 33.351, revenue_rate: 0.8109, distance: 10.0675, memory: -0.0169, power: 0.3052, lr: 0.000100, took: 85.484s
[COOPERATIVE] Epoch 1, Batch 1240/1563, loss: 3.404, reward: 31.761, critic_reward: 31.231, revenue_rate: 0.7811, distance: 9.5435, memory: -0.0390, power: 0.2888, lr: 0.000100, took: 82.791s
[COOPERATIVE] Epoch 1, Batch 1250/1563, loss: 3.533, reward: 32.434, critic_reward: 32.877, revenue_rate: 0.7959, distance: 9.8165, memory: -0.0334, power: 0.2974, lr: 0.000100, took: 83.611s
[COOPERATIVE] Epoch 1, Batch 1260/1563, loss: 2.341, reward: 31.750, critic_reward: 31.840, revenue_rate: 0.7825, distance: 9.6037, memory: -0.0286, power: 0.2915, lr: 0.000100, took: 81.081s
[COOPERATIVE] Epoch 1, Batch 1270/1563, loss: 5.237, reward: 31.368, critic_reward: 30.789, revenue_rate: 0.7705, distance: 9.4839, memory: -0.0358, power: 0.2866, lr: 0.000100, took: 79.624s
[COOPERATIVE] Epoch 1, Batch 1280/1563, loss: 7.058, reward: 32.901, critic_reward: 34.221, revenue_rate: 0.8100, distance: 10.1841, memory: -0.0036, power: 0.3078, lr: 0.000100, took: 86.991s
[COOPERATIVE] Epoch 1, Batch 1290/1563, loss: 9.178, reward: 32.281, critic_reward: 33.806, revenue_rate: 0.7922, distance: 9.7675, memory: -0.0273, power: 0.2963, lr: 0.000100, took: 83.118s
[COOPERATIVE] Epoch 1, Batch 1300/1563, loss: 5.843, reward: 32.334, critic_reward: 31.558, revenue_rate: 0.7958, distance: 9.8069, memory: -0.0240, power: 0.2954, lr: 0.000100, took: 83.406s
[COOPERATIVE] Epoch 1, Batch 1310/1563, loss: 3.500, reward: 33.930, critic_reward: 34.281, revenue_rate: 0.8362, distance: 10.5311, memory: -0.0093, power: 0.3183, lr: 0.000100, took: 90.819s
[COOPERATIVE] Epoch 1, Batch 1320/1563, loss: 4.433, reward: 34.373, critic_reward: 34.218, revenue_rate: 0.8511, distance: 10.8647, memory: 0.0075, power: 0.3285, lr: 0.000100, took: 95.425s
[COOPERATIVE] Epoch 1, Batch 1330/1563, loss: 3.764, reward: 33.705, critic_reward: 33.407, revenue_rate: 0.8272, distance: 10.4813, memory: -0.0119, power: 0.3162, lr: 0.000100, took: 89.379s
[COOPERATIVE] Epoch 1, Batch 1340/1563, loss: 5.598, reward: 33.788, critic_reward: 34.131, revenue_rate: 0.8313, distance: 10.4574, memory: -0.0037, power: 0.3155, lr: 0.000100, took: 88.722s
[COOPERATIVE] Epoch 1, Batch 1350/1563, loss: 4.203, reward: 31.186, critic_reward: 30.476, revenue_rate: 0.7662, distance: 9.4195, memory: -0.0413, power: 0.2846, lr: 0.000100, took: 81.715s
[COOPERATIVE] Epoch 1, Batch 1360/1563, loss: 3.187, reward: 33.010, critic_reward: 32.851, revenue_rate: 0.8124, distance: 10.1149, memory: -0.0246, power: 0.3076, lr: 0.000100, took: 86.071s
[COOPERATIVE] Epoch 1, Batch 1370/1563, loss: 8.352, reward: 32.543, critic_reward: 34.471, revenue_rate: 0.7996, distance: 9.8794, memory: -0.0302, power: 0.2999, lr: 0.000100, took: 84.269s
[COOPERATIVE] Epoch 1, Batch 1380/1563, loss: 4.193, reward: 29.579, critic_reward: 28.514, revenue_rate: 0.7250, distance: 8.8156, memory: -0.0674, power: 0.2668, lr: 0.000100, took: 73.148s
[COOPERATIVE] Epoch 1, Batch 1390/1563, loss: 6.147, reward: 29.046, critic_reward: 30.479, revenue_rate: 0.7092, distance: 8.4538, memory: -0.0700, power: 0.2581, lr: 0.000100, took: 70.539s
