"""
多星座模式训练脚本
一次性训练三种星座模式（cooperative, competitive, hybrid）并对比结果
"""
import os
import sys
import torch
import datetime
import numpy as np
import matplotlib.pyplot as plt
import json
import time
from torch.utils.data import DataLoader
from torch import optim
from torch.optim.lr_scheduler import ReduceLROnPlateau

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.gpn_constellation import GPNConstellation, ConstellationStateCritic
from train_constellation import validate_constellation_smp
from pict import plot_single_smp_train_loss
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 全局日志文件
log_file = None

def log_message(message):
    """记录日志信息"""
    print(message)
    if log_file is not None:
        log_file.write(message + '\n')
        log_file.flush()


class TrainingLogger:
    """训练过程详细日志记录器"""
    def __init__(self, constellation_mode):
        self.constellation_mode = constellation_mode
        self.current_epoch = 0
        self.batch_count = 0

    def log_batch_info(self, epoch, batch_idx, total_batches, loss, reward, critic_reward,
                      revenue_rate, distance, memory, power, lr, batch_time):
        """记录批次训练信息"""
        if (batch_idx + 1) % 10 == 0:  # 每10个批次记录一次
            message = (f"[{self.constellation_mode.upper()}] "
                      f"Epoch {epoch+1}, Batch {batch_idx+1}/{total_batches}, "
                      f"loss: {loss:.3f}, reward: {reward:.3f}, critic_reward: {critic_reward:.3f}, "
                      f"revenue_rate: {revenue_rate:.4f}, distance: {distance:.4f}, "
                      f"memory: {memory:.4f}, power: {power:.4f}, "
                      f"lr: {lr:.6f}, took: {batch_time:.3f}s")
            log_message(message)

    def log_epoch_start(self, epoch, total_epochs):
        """记录epoch开始"""
        self.current_epoch = epoch
        log_message(f"[{self.constellation_mode.upper()}] 开始训练 Epoch {epoch+1}/{total_epochs}")

    def log_validation_start(self):
        """记录验证开始"""
        log_message(f"[{self.constellation_mode.upper()}] 开始验证...")

    def log_validation_result(self, epoch, reward, revenue_rate, distance, memory, power):
        """记录验证结果"""
        message = (f"[{self.constellation_mode.upper()}] "
                  f"验证完成 - Epoch {epoch+1}, reward: {reward:.3f}, "
                  f"revenue_rate: {revenue_rate:.4f}, distance: {distance:.4f}, "
                  f"memory: {memory:.4f}, power: {power:.4f}")
        log_message(message)

    def log_model_save(self, epoch, reward, save_path):
        """记录模型保存"""
        log_message(f"[{self.constellation_mode.upper()}] "
                   f"已保存新模型到 {save_path} (验证集奖励: {reward:.4f})")

    def log_training_complete(self):
        """记录训练完成"""
        log_message(f"[{self.constellation_mode.upper()}] 训练完成")


def train_constellation_with_detailed_logging(actor, critic, logger, task, num_nodes, train_data, valid_data,
                                            reward_fn, render_fn, batch_size, actor_lr, critic_lr,
                                            max_grad_norm, attention, epochs, num_satellites, save_dir,
                                            weight_decay, verbose=False, constellation_mode='cooperative', **kwargs):
    """
    带详细日志记录的星座任务规划模型训练过程
    """
    # 定义优化器
    actor_optim = optim.Adam(actor.parameters(), lr=actor_lr, weight_decay=weight_decay)
    critic_optim = optim.Adam(critic.parameters(), lr=critic_lr, weight_decay=weight_decay)

    # 学习率调度器
    actor_scheduler_plateau = ReduceLROnPlateau(actor_optim, 'max', factor=0.5, patience=5, verbose=True)
    critic_scheduler_plateau = ReduceLROnPlateau(critic_optim, 'max', factor=0.5, patience=5, verbose=True)

    # 数据加载器
    train_loader = DataLoader(train_data, batch_size, True, num_workers=0)
    valid_loader = DataLoader(valid_data, batch_size, False, num_workers=0)

    # 训练统计
    best_reward = float('-inf')
    losses, rewards, critic_rewards = [], [], []
    revenue_rates, distances, memories, powers = [], [], [], []
    times = []

    for epoch in range(epochs):
        logger.log_epoch_start(epoch, epochs)

        actor.train()
        critic.train()
        epoch_start = time.time()
        start = epoch_start

        for batch_idx, batch in enumerate(train_loader):
            static, dynamic, x0 = batch
            static = static.to(device)
            dynamic = dynamic.to(device)

            # 前向传播
            tour_indices, satellite_indices, tour_logp, satellite_logp = actor(static, dynamic)

            # 计算奖励
            R, revenue_rate, distance, memory, power = reward_fn(static, tour_indices, satellite_indices, constellation_mode)

            # Critic评估
            baseline = critic(static, dynamic).view(-1)

            # 计算损失
            advantage = (R - baseline).detach()
            actor_loss = torch.mean(advantage * (tour_logp.sum(dim=1) + satellite_logp.sum(dim=1)))
            critic_loss = torch.mean((R - baseline) ** 2)

            # 反向传播
            actor_optim.zero_grad()
            actor_loss.backward()
            torch.nn.utils.clip_grad_norm_(actor.parameters(), max_grad_norm)
            actor_optim.step()

            critic_optim.zero_grad()
            critic_loss.backward()
            torch.nn.utils.clip_grad_norm_(critic.parameters(), max_grad_norm)
            critic_optim.step()

            # 记录统计信息
            losses.append(actor_loss.item())
            rewards.append(torch.mean(R).item())
            critic_rewards.append(torch.mean(baseline).item())
            revenue_rates.append(torch.mean(revenue_rate).item())
            distances.append(torch.mean(distance).item())
            memories.append(torch.mean(memory).item())
            powers.append(torch.mean(power).item())

            # 详细日志记录
            if (batch_idx + 1) % 10 == 0:
                end = time.time()
                batch_time = end - start
                times.append(batch_time)
                start = end

                # 使用logger记录详细信息
                logger.log_batch_info(
                    epoch, batch_idx, len(train_loader),
                    np.mean(losses[-10:]), np.mean(rewards[-10:]), np.mean(critic_rewards[-10:]),
                    np.mean(revenue_rates[-10:]), np.mean(distances[-10:]),
                    np.mean(memories[-10:]), np.mean(powers[-10:]),
                    actor_optim.param_groups[0]['lr'], batch_time
                )

        # 验证
        logger.log_validation_start()
        valid_reward, valid_revenue_rate, valid_distance, valid_memory, valid_power = validate_constellation_smp(
            valid_loader, actor, reward_fn, num_satellites, render_fn,
            os.path.join(save_dir, f'epoch_{epoch}'), num_plot=5, verbose=verbose,
            constellation_mode=constellation_mode
        )

        logger.log_validation_result(epoch, valid_reward, valid_revenue_rate, valid_distance, valid_memory, valid_power)

        # 保存最佳模型
        if valid_reward > best_reward:
            best_reward = valid_reward
            torch.save(actor.state_dict(), os.path.join(save_dir, 'actor.pt'))
            torch.save(critic.state_dict(), os.path.join(save_dir, 'critic.pt'))
            logger.log_model_save(epoch, valid_reward, save_dir)

        # 更新学习率
        actor_scheduler_plateau.step(valid_reward)
        critic_scheduler_plateau.step(valid_reward)

    logger.log_training_complete()

    # 返回训练统计
    training_stats = {
        'rewards': rewards,
        'revenue_rates': revenue_rates,
        'distances': distances,
        'memories': memories,
        'powers': powers,
        'losses': losses,
        'critic_rewards': critic_rewards,
        'times': times
    }

    return best_reward, training_stats


def create_model_for_mode(constellation_mode, train_data, transformer_config=None):
    """为指定星座模式创建模型"""
    actor = GPNConstellation(
        args.static_size,
        args.dynamic_size,
        args.hidden_size,
        args.num_satellites,
        args.rnn,
        args.num_layers,
        train_data.update_dynamic,
        train_data.update_mask,
        args.num_nodes,
        args.dropout,
        constellation_mode,
        args.use_transformer,
        transformer_config
    ).to(device)
    
    critic = ConstellationStateCritic(
        args.static_size,
        args.dynamic_size,
        args.hidden_size,
        args.num_satellites,
        constellation_mode
    ).to(device)
    
    return actor, critic


def train_single_mode(constellation_mode, base_save_dir, transformer_config=None):
    """训练单个星座模式"""
    log_message(f"\n{'='*80}")
    log_message(f"开始训练星座模式: {constellation_mode.upper()}")
    log_message(f"{'='*80}")
    
    # 创建该模式的保存目录
    now = datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    mode_folder_name = f'constellation_{args.model}{args.rnn}_{constellation_mode}'
    
    if args.use_transformer:
        mode_folder_name += f'_transformer_L{args.transformer_layers}H{args.transformer_heads}'
    
    mode_folder_name += f'_{now}'
    mode_save_dir = os.path.join(base_save_dir, mode_folder_name)
    
    if not os.path.exists(mode_save_dir):
        os.makedirs(mode_save_dir)
    
    # 创建数据集
    train_data = ConstellationSMPDataset(
        args.num_nodes,
        args.train_size,
        args.seed,
        args.memory_total,
        args.power_total,
        args.num_satellites
    )
    
    valid_data = ConstellationSMPDataset(
        args.num_nodes,
        args.valid_size,
        args.seed + 1,
        args.memory_total,
        args.power_total,
        args.num_satellites
    )
    
    # 创建模型
    actor, critic = create_model_for_mode(constellation_mode, train_data, transformer_config)
    
    actor_params = sum(p.numel() for p in actor.parameters())
    critic_params = sum(p.numel() for p in critic.parameters())
    
    log_message(f"{constellation_mode} 模式模型信息:")
    log_message(f"  Actor参数数量: {actor_params:,}")
    log_message(f"  Critic参数数量: {critic_params:,}")
    log_message(f"  总参数数量: {actor_params + critic_params:,}")
    
    # 准备训练参数
    kwargs = {
        'task': args.task,
        'num_nodes': args.num_nodes,
        'train_data': train_data,
        'valid_data': valid_data,
        'reward_fn': reward,
        'render_fn': render,
        'batch_size': args.batch_size,
        'actor_lr': args.actor_lr,
        'critic_lr': args.critic_lr,
        'max_grad_norm': args.max_grad_norm,
        'attention': args.attention,
        'epochs': args.epochs,
        'num_satellites': args.num_satellites,
        'save_dir': mode_save_dir,
        'weight_decay': args.weight_decay,
        'verbose': args.verbose,
        'constellation_mode': constellation_mode
    }

    # 记录详细的训练配置
    log_message(f"详细训练配置:")
    log_message(f"  数据集大小: 训练{args.train_size}, 验证{args.valid_size}")
    log_message(f"  学习率: Actor={args.actor_lr}, Critic={args.critic_lr}")
    log_message(f"  批次大小: {args.batch_size}")
    log_message(f"  训练轮数: {args.epochs}")
    log_message(f"  梯度裁剪: {args.max_grad_norm}")
    log_message(f"  权重衰减: {args.weight_decay}")
    log_message(f"  随机种子: {args.seed}")
    log_message(f"  内存总量: {args.memory_total}")
    log_message(f"  功率总量: {args.power_total}")

    # 创建详细日志记录器
    training_logger = TrainingLogger(constellation_mode)

    # 训练模型
    log_message(f"开始训练 {constellation_mode} 模式...")
    training_start_time = datetime.datetime.now()
    log_message(f"训练开始时间: {training_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    log_message(f"详细训练过程:")

    # 使用自定义训练函数以获取详细日志
    best_reward, training_stats = train_constellation_with_detailed_logging(
        actor, critic, training_logger, **kwargs)

    training_end_time = datetime.datetime.now()
    training_duration = training_end_time - training_start_time
    log_message(f"训练结束时间: {training_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    log_message(f"训练总耗时: {training_duration}")

    # 记录训练统计信息
    if training_stats and 'rewards' in training_stats:
        log_message(f"训练过程统计:")
        log_message(f"  最终训练奖励: {training_stats['rewards'][-1]:.4f}")
        log_message(f"  最佳验证奖励: {best_reward:.4f}")
        log_message(f"  训练轮数完成: {len(training_stats['rewards'])}")

        if len(training_stats['rewards']) > 1:
            improvement = training_stats['rewards'][-1] - training_stats['rewards'][0]
            log_message(f"  奖励提升: {improvement:.4f}")
            log_message(f"  平均每轮提升: {improvement/len(training_stats['rewards']):.4f}")

        # 绘制训练曲线
        log_message(f"生成训练曲线图...")
        try:
            # 准备时间数据（如果没有times，使用步数）
            if 'times' in training_stats and training_stats['times']:
                times = np.array(training_stats['times']).cumsum()
            else:
                times = np.arange(len(training_stats['rewards']))

            # 调用绘图函数
            plot_single_smp_train_loss(
                mode_save_dir,
                times,
                training_stats.get('losses', [0] * len(training_stats['rewards'])),
                training_stats['rewards'],
                training_stats.get('critic_rewards', [0] * len(training_stats['rewards'])),
                training_stats.get('revenue_rates', [0] * len(training_stats['rewards'])),
                training_stats.get('distances', [0] * len(training_stats['rewards'])),
                training_stats.get('memories', [0] * len(training_stats['rewards'])),
                training_stats.get('powers', [0] * len(training_stats['rewards']))
            )
            log_message(f"✓ 训练曲线图已保存到: {os.path.join(mode_save_dir, 'train_loss_reward.png')}")
        except Exception as e:
            log_message(f"⚠️ 生成训练曲线图时出错: {e}")
    else:
        log_message(f"  最佳验证奖励: {best_reward:.4f}")
        log_message(f"  ⚠️ 训练统计信息不完整，无法生成训练曲线图")
    
    # 测试模型
    test_data = ConstellationSMPDataset(
        args.num_nodes,
        args.valid_size,
        args.seed + 2,
        args.memory_total,
        args.power_total,
        args.num_satellites
    )
    
    test_dir = os.path.join(mode_save_dir, f'test_{constellation_mode}')
    test_loader = DataLoader(test_data, args.batch_size, False, num_workers=0)

    log_message(f"开始测试 {constellation_mode} 模式...")
    log_message(f"测试配置:")
    log_message(f"  测试数据大小: {args.valid_size}")
    log_message(f"  测试批次数: {len(test_loader)}")
    log_message(f"  可视化样本数: 5")

    test_start_time = datetime.datetime.now()
    log_message(f"测试开始时间: {test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    _, revenue_rate_avg, distance_avg, memory_avg, power_avg = validate_constellation_smp(
        test_loader, actor, reward, args.num_satellites, render,
        test_dir, num_plot=5, verbose=args.verbose,
        constellation_mode=constellation_mode
    )

    test_end_time = datetime.datetime.now()
    test_duration = test_end_time - test_start_time
    log_message(f"测试结束时间: {test_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    log_message(f"测试耗时: {test_duration}")
    
    # 返回结果
    results = {
        'constellation_mode': constellation_mode,
        'best_reward': best_reward,
        'revenue_rate_avg': revenue_rate_avg,
        'distance_avg': distance_avg,
        'memory_avg': memory_avg,
        'power_avg': power_avg,
        'actor_params': actor_params,
        'critic_params': critic_params,
        'total_params': actor_params + critic_params,
        'training_stats': training_stats,
        'save_dir': mode_save_dir
    }
    
    log_message(f"\n{constellation_mode.upper()} 模式完整结果:")
    log_message(f"{'='*50}")
    log_message(f"训练结果:")
    log_message(f"  最佳验证奖励: {best_reward:.4f}")
    log_message(f"  模型保存路径: {mode_save_dir}")

    log_message(f"测试结果:")
    log_message(f"  平均收益率: {revenue_rate_avg:.4f}")
    log_message(f"  平均距离: {distance_avg:.4f}")
    log_message(f"  平均内存使用: {memory_avg:.4f}")
    log_message(f"  平均功耗: {power_avg:.4f}")

    log_message(f"模型信息:")
    log_message(f"  Actor参数: {actor_params:,}")
    log_message(f"  Critic参数: {critic_params:,}")
    log_message(f"  总参数: {actor_params + critic_params:,}")

    # 计算性能评分
    performance_score = revenue_rate_avg * 10 - distance_avg * 0.5 - abs(memory_avg) * 0.5 - power_avg * 0.5
    log_message(f"综合性能评分: {performance_score:.4f}")

    log_message(f"文件输出:")
    log_message(f"  权重文件: actor.pt, critic.pt")
    log_message(f"  训练日志: log.txt")
    log_message(f"  测试结果: test_{constellation_mode}/")
    log_message(f"{'='*50}")

    return results


def plot_multi_mode_training_curves(all_results, comparison_dir):
    """为多星座模式创建训练曲线对比图"""
    log_message(f"生成多模式训练曲线对比图...")

    try:
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
        plt.rcParams['axes.unicode_minus'] = False

        # 创建训练曲线对比图
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        axes = axes.flatten()

        # 定义要绘制的指标
        metrics = ['rewards', 'losses', 'revenue_rates', 'distances', 'memories', 'powers', 'critic_rewards']
        metric_names = ['奖励 (Reward)', '损失 (Loss)', '收益率 (Revenue Rate)', '距离 (Distance)',
                       '内存使用 (Memory)', '功耗 (Power)', 'Critic奖励 (Critic Reward)']
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink']

        for i, (metric, name) in enumerate(zip(metrics, metric_names)):
            if i < len(axes):
                ax = axes[i]

                for j, result in enumerate(all_results):
                    mode = result['constellation_mode']
                    stats = result.get('training_stats', {})

                    if metric in stats and len(stats[metric]) > 0:
                        # 平滑曲线
                        data = stats[metric]
                        if len(data) > 10:
                            # 使用简单的移动平均进行平滑
                            window = min(10, len(data) // 5)
                            smoothed_data = []
                            for k in range(len(data)):
                                start = max(0, k - window // 2)
                                end = min(len(data), k + window // 2 + 1)
                                smoothed_data.append(np.mean(data[start:end]))
                            data = smoothed_data

                        epochs = range(1, len(data) + 1)
                        ax.plot(epochs, data, label=mode.capitalize(),
                               color=colors[j % len(colors)], linewidth=2, marker='o', markersize=3)

                ax.set_title(name, fontsize=12, fontweight='bold')
                ax.set_xlabel('训练步数 (Steps)', fontsize=10)
                ax.set_ylabel(name, fontsize=10)
                ax.legend(fontsize=9)
                ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for i in range(len(metrics), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        plt.savefig(os.path.join(comparison_dir, 'multi_mode_training_curves.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

        log_message(f"✓ 多模式训练曲线图已保存")

    except Exception as e:
        log_message(f"⚠️ 生成多模式训练曲线图时出错: {e}")


def create_comparison_plots(all_results, comparison_dir):
    """创建对比图表"""
    log_message(f"\n创建对比图表...")
    
    modes = [result['constellation_mode'] for result in all_results]
    
    # 1. 性能指标对比
    metrics = ['best_reward', 'revenue_rate_avg', 'distance_avg', 'memory_avg', 'power_avg']
    metric_names = ['最佳奖励', '平均收益率', '平均距离', '平均内存使用', '平均功耗']
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, (metric, name) in enumerate(zip(metrics, metric_names)):
        values = [result[metric] for result in all_results]
        
        bars = axes[i].bar(modes, values, alpha=0.7)
        axes[i].set_title(name)
        axes[i].set_ylabel('数值')
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            axes[i].text(bar.get_x() + bar.get_width()/2., height,
                        f'{value:.4f}', ha='center', va='bottom')
    
    # 2. 模型复杂度对比
    param_counts = [result['total_params'] for result in all_results]
    bars = axes[5].bar(modes, param_counts, alpha=0.7, color='orange')
    axes[5].set_title('模型参数数量')
    axes[5].set_ylabel('参数数量')
    
    for bar, count in zip(bars, param_counts):
        height = bar.get_height()
        axes[5].text(bar.get_x() + bar.get_width()/2., height,
                    f'{count:,}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(os.path.join(comparison_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 训练曲线对比
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    for result in all_results:
        mode = result['constellation_mode']
        stats = result['training_stats']
        
        if 'rewards' in stats and len(stats['rewards']) > 0:
            epochs = range(1, len(stats['rewards']) + 1)
            
            axes[0, 0].plot(epochs, stats['rewards'], label=mode, marker='o')
            axes[0, 1].plot(epochs, stats['revenue_rates'], label=mode, marker='s')
            axes[1, 0].plot(epochs, stats['distances'], label=mode, marker='^')
            axes[1, 1].plot(epochs, stats['powers'], label=mode, marker='d')
    
    axes[0, 0].set_title('训练奖励曲线')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('奖励')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    axes[0, 1].set_title('收益率曲线')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('收益率')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    axes[1, 0].set_title('距离曲线')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('距离')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    axes[1, 1].set_title('功耗曲线')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('功耗')
    axes[1, 1].legend()
    axes[1, 1].grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(comparison_dir, 'training_curves_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    log_message(f"对比图表已保存到: {comparison_dir}")


def save_detailed_training_log(all_results, base_save_dir):
    """保存详细的训练日志到单独文件"""
    detailed_log_path = os.path.join(base_save_dir, 'detailed_training_summary.txt')

    with open(detailed_log_path, 'w', encoding='utf-8') as f:
        f.write("多星座模式训练详细日志\n")
        f.write("=" * 80 + "\n\n")

        f.write(f"实验时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"实验配置:\n")
        f.write(f"  问题规模: {args.num_nodes}节点, {args.num_satellites}卫星\n")
        f.write(f"  训练参数: {args.epochs}轮, 批次{args.batch_size}, 学习率{args.actor_lr}\n")
        f.write(f"  数据规模: 训练{args.train_size}, 验证{args.valid_size}\n")
        f.write(f"  使用Transformer: {args.use_transformer}\n")
        if args.use_transformer:
            f.write(f"  Transformer配置: {args.transformer_layers}层, {args.transformer_heads}头\n")
        f.write("\n")

        for result in all_results:
            mode = result['constellation_mode']
            f.write(f"{mode.upper()} 模式详细结果:\n")
            f.write("-" * 50 + "\n")

            f.write(f"模型架构:\n")
            f.write(f"  Actor参数数量: {result['actor_params']:,}\n")
            f.write(f"  Critic参数数量: {result['critic_params']:,}\n")
            f.write(f"  总参数数量: {result['total_params']:,}\n")

            f.write(f"训练结果:\n")
            f.write(f"  最佳验证奖励: {result['best_reward']:.6f}\n")

            f.write(f"测试性能:\n")
            f.write(f"  平均收益率: {result['revenue_rate_avg']:.6f}\n")
            f.write(f"  平均距离: {result['distance_avg']:.6f}\n")
            f.write(f"  平均内存使用: {result['memory_avg']:.6f}\n")
            f.write(f"  平均功耗: {result['power_avg']:.6f}\n")

            # 计算性能评分
            performance_score = (result['revenue_rate_avg'] * 10 -
                               result['distance_avg'] * 0.5 -
                               abs(result['memory_avg']) * 0.5 -
                               result['power_avg'] * 0.5)
            f.write(f"  综合性能评分: {performance_score:.6f}\n")

            f.write(f"文件路径:\n")
            f.write(f"  模型保存: {result['save_dir']}\n")
            f.write(f"  权重文件: actor.pt, critic.pt\n")
            f.write(f"  训练日志: log.txt\n")
            f.write(f"  测试结果: test_{mode}/\n")
            f.write("\n")

        # 对比分析
        f.write("对比分析:\n")
        f.write("=" * 50 + "\n")

        best_reward = max(all_results, key=lambda x: x['best_reward'])
        best_revenue = max(all_results, key=lambda x: x['revenue_rate_avg'])
        min_distance = min(all_results, key=lambda x: x['distance_avg'])
        min_power = min(all_results, key=lambda x: x['power_avg'])

        f.write(f"最佳奖励模式: {best_reward['constellation_mode']} ({best_reward['best_reward']:.4f})\n")
        f.write(f"最佳收益率模式: {best_revenue['constellation_mode']} ({best_revenue['revenue_rate_avg']:.4f})\n")
        f.write(f"最短距离模式: {min_distance['constellation_mode']} ({min_distance['distance_avg']:.4f})\n")
        f.write(f"最低功耗模式: {min_power['constellation_mode']} ({min_power['power_avg']:.4f})\n")

        f.write(f"\n推荐使用: {best_reward['constellation_mode']} 模式\n")
        f.write(f"推荐理由: 在关键性能指标上表现最佳\n")

    log_message(f"详细训练日志已保存到: {detailed_log_path}")


def save_comparison_results(all_results, comparison_dir):
    """保存对比结果"""
    # 保存详细结果到JSON文件
    results_summary = {
        'experiment_info': {
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'num_nodes': args.num_nodes,
            'num_satellites': args.num_satellites,
            'epochs': args.epochs,
            'train_size': args.train_size,
            'valid_size': args.valid_size,
            'use_transformer': args.use_transformer,
            'transformer_config': {
                'layers': args.transformer_layers,
                'heads': args.transformer_heads,
                'd_model': args.transformer_d_model,
                'd_ff': args.transformer_d_ff,
                'dropout': args.transformer_dropout,
                'activation': args.transformer_activation
            } if args.use_transformer else None
        },
        'results': []
    }
    
    for result in all_results:
        # 移除不能序列化的训练统计数据
        result_copy = result.copy()
        if 'training_stats' in result_copy:
            # 转换tensor为list
            stats = result_copy['training_stats']
            for key, value in stats.items():
                if hasattr(value, 'tolist'):
                    stats[key] = value.tolist()
                elif isinstance(value, list) and len(value) > 0 and hasattr(value[0], 'item'):
                    stats[key] = [v.item() if hasattr(v, 'item') else v for v in value]
        
        results_summary['results'].append(result_copy)
    
    # 保存JSON文件
    json_path = os.path.join(comparison_dir, 'comparison_results.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False)
    
    # 保存简化的文本报告
    report_path = os.path.join(comparison_dir, 'comparison_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("多星座模式训练对比报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"实验时间: {results_summary['experiment_info']['timestamp']}\n")
        f.write(f"问题规模: {args.num_nodes}节点, {args.num_satellites}卫星\n")
        f.write(f"训练轮数: {args.epochs}\n")
        f.write(f"使用Transformer: {args.use_transformer}\n\n")
        
        f.write("性能对比结果:\n")
        f.write("-" * 30 + "\n")
        
        for result in all_results:
            f.write(f"\n{result['constellation_mode'].upper()} 模式:\n")
            f.write(f"  最佳奖励: {result['best_reward']:.4f}\n")
            f.write(f"  平均收益率: {result['revenue_rate_avg']:.4f}\n")
            f.write(f"  平均距离: {result['distance_avg']:.4f}\n")
            f.write(f"  平均内存使用: {result['memory_avg']:.4f}\n")
            f.write(f"  平均功耗: {result['power_avg']:.4f}\n")
            f.write(f"  模型参数: {result['total_params']:,}\n")
        
        # 找出最佳模式
        best_reward_mode = max(all_results, key=lambda x: x['best_reward'])
        best_revenue_mode = max(all_results, key=lambda x: x['revenue_rate_avg'])
        
        f.write(f"\n最佳性能:\n")
        f.write(f"  最高奖励: {best_reward_mode['constellation_mode']} ({best_reward_mode['best_reward']:.4f})\n")
        f.write(f"  最高收益率: {best_revenue_mode['constellation_mode']} ({best_revenue_mode['revenue_rate_avg']:.4f})\n")
    
    log_message(f"对比结果已保存到:")
    log_message(f"  JSON文件: {json_path}")
    log_message(f"  文本报告: {report_path}")


def main():
    """主训练函数"""
    global log_file
    
    # 创建主保存目录
    now = datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    base_folder_name = f'multi_constellation_comparison'
    
    if args.use_transformer:
        base_folder_name += f'_transformer_L{args.transformer_layers}H{args.transformer_heads}'
    
    base_folder_name += f'_{now}'
    
    base_save_dir = os.path.join(args.task, args.task + '%d' % args.num_nodes, base_folder_name)
    if not os.path.exists(base_save_dir):
        os.makedirs(base_save_dir)
    
    # 创建对比结果目录
    comparison_dir = os.path.join(base_save_dir, 'comparison_results')
    if not os.path.exists(comparison_dir):
        os.makedirs(comparison_dir)
    
    # 创建全局日志文件
    log_path = os.path.join(base_save_dir, 'multi_mode_training_log.txt')
    log_file = open(log_path, 'w', encoding='utf-8')
    
    # 记录实验配置
    log_message("多星座模式训练实验")
    log_message("=" * 80)
    log_message(f"实验时间: {now}")
    log_message(f"设备: {device}")
    log_message(f"问题规模: {args.num_nodes}节点, {args.num_satellites}卫星")
    log_message(f"训练配置: {args.epochs}轮, 批次大小{args.batch_size}")
    log_message(f"使用Transformer: {args.use_transformer}")
    
    if args.use_transformer:
        log_message(f"Transformer配置: {args.transformer_layers}层, {args.transformer_heads}头")
    
    # 准备Transformer配置
    transformer_config = None
    if args.use_transformer:
        transformer_config = {
            'd_model': args.transformer_d_model,
            'num_heads': args.transformer_heads,
            'd_ff': args.transformer_d_ff,
            'num_layers': args.transformer_layers,
            'max_len': args.transformer_max_len,
            'dropout': args.transformer_dropout,
            'activation': args.transformer_activation
        }
    
    # 训练三种星座模式
    constellation_modes = ['cooperative', 'competitive', 'hybrid']
    all_results = []
    
    for mode in constellation_modes:
        try:
            result = train_single_mode(mode, base_save_dir, transformer_config)
            all_results.append(result)
        except Exception as e:
            log_message(f"❌ {mode} 模式训练失败: {e}")
            import traceback
            log_message(traceback.format_exc())
    
    if len(all_results) > 0:
        # 创建对比图表和报告
        log_message(f"\n{'='*80}")
        log_message("生成对比分析")
        log_message(f"{'='*80}")
        
        plot_multi_mode_training_curves(all_results, comparison_dir)
        create_comparison_plots(all_results, comparison_dir)
        save_comparison_results(all_results, comparison_dir)
        save_detailed_training_log(all_results, base_save_dir)
        
        # 输出详细总结
        log_message(f"\n{'='*80}")
        log_message("多星座模式训练实验总结")
        log_message(f"{'='*80}")

        experiment_end_time = datetime.datetime.now()
        total_duration = experiment_end_time - datetime.datetime.strptime(now, '%Y_%m_%d_%H_%M_%S')
        log_message(f"实验总耗时: {total_duration}")
        log_message(f"成功训练模式数: {len(all_results)}/3")

        log_message(f"\n各模式详细对比:")
        log_message(f"{'模式':<12} {'奖励':<8} {'收益率':<8} {'距离':<8} {'内存':<8} {'功耗':<8} {'参数数':<10}")
        log_message(f"{'-'*70}")

        for result in all_results:
            log_message(f"{result['constellation_mode']:<12} "
                       f"{result['best_reward']:<8.4f} "
                       f"{result['revenue_rate_avg']:<8.4f} "
                       f"{result['distance_avg']:<8.4f} "
                       f"{result['memory_avg']:<8.4f} "
                       f"{result['power_avg']:<8.4f} "
                       f"{result['total_params']:<10,}")

        # 找出各项最佳
        best_reward_mode = max(all_results, key=lambda x: x['best_reward'])
        best_revenue_mode = max(all_results, key=lambda x: x['revenue_rate_avg'])
        min_distance_mode = min(all_results, key=lambda x: x['distance_avg'])
        min_power_mode = min(all_results, key=lambda x: x['power_avg'])

        log_message(f"\n性能排名:")
        log_message(f"🏆 最高奖励: {best_reward_mode['constellation_mode'].upper()} ({best_reward_mode['best_reward']:.4f})")
        log_message(f"💰 最高收益率: {best_revenue_mode['constellation_mode'].upper()} ({best_revenue_mode['revenue_rate_avg']:.4f})")
        log_message(f"🚀 最短距离: {min_distance_mode['constellation_mode'].upper()} ({min_distance_mode['distance_avg']:.4f})")
        log_message(f"⚡ 最低功耗: {min_power_mode['constellation_mode'].upper()} ({min_power_mode['power_avg']:.4f})")

        # 推荐最佳模式
        if best_reward_mode == best_revenue_mode:
            log_message(f"\n💡 推荐模式: {best_reward_mode['constellation_mode'].upper()}")
            log_message(f"   理由: 在奖励和收益率两个关键指标上都表现最佳")
        else:
            log_message(f"\n💡 推荐模式分析:")
            log_message(f"   如果追求最高奖励: {best_reward_mode['constellation_mode'].upper()}")
            log_message(f"   如果追求最高收益率: {best_revenue_mode['constellation_mode'].upper()}")

        log_message(f"\n📁 实验结果文件:")
        log_message(f"   主目录: {base_save_dir}")
        log_message(f"   对比分析: {comparison_dir}")
        log_message(f"   全局日志: {os.path.join(base_save_dir, 'multi_mode_training_log.txt')}")

        for result in all_results:
            log_message(f"   {result['constellation_mode']} 模式: {result['save_dir']}")
    
    else:
        log_message("❌ 所有模式训练都失败了")
    
    if log_file:
        log_file.close()


def print_usage():
    """打印使用说明"""
    print("多星座模式训练脚本使用说明:")
    print("=" * 50)
    print("基本使用:")
    print("  python train_multi_constellation_modes.py")
    print()
    print("使用Transformer:")
    print("  python train_multi_constellation_modes.py --use_transformer")
    print()
    print("自定义配置:")
    print("  python train_multi_constellation_modes.py --epochs 10 --batch_size 64")
    print()
    print("功能特点:")
    print("  ✓ 自动训练三种星座模式 (cooperative, competitive, hybrid)")
    print("  ✓ 生成详细的性能对比图表")
    print("  ✓ 保存完整的实验报告")
    print("  ✓ 支持Transformer增强模型")
    print("  ✓ 自动创建带时间戳的结果目录")
    print()
    print("输出文件:")
    print("  📁 multi_constellation_comparison_TIMESTAMP/")
    print("    ├── constellation_gpnindrnn_cooperative_TIMESTAMP/")
    print("    ├── constellation_gpnindrnn_competitive_TIMESTAMP/")
    print("    ├── constellation_gpnindrnn_hybrid_TIMESTAMP/")
    print("    ├── comparison_results/")
    print("    │   ├── performance_comparison.png")
    print("    │   ├── training_curves_comparison.png")
    print("    │   ├── comparison_results.json")
    print("    │   └── comparison_report.txt")
    print("    └── multi_mode_training_log.txt")


if __name__ == '__main__':
    import argparse

    # 检查是否需要显示帮助
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
        sys.exit(0)

    print("🚀 多星座模式训练脚本")
    print("=" * 50)
    print(f"当前配置:")
    print(f"  节点数: {args.num_nodes}")
    print(f"  卫星数: {args.num_satellites}")
    print(f"  训练轮数: {args.epochs}")
    print(f"  批次大小: {args.batch_size}")
    print(f"  使用Transformer: {args.use_transformer}")
    if args.use_transformer:
        print(f"  Transformer层数: {args.transformer_layers}")
        print(f"  注意力头数: {args.transformer_heads}")
    print()

    try:
        main()
        print("\n🎉 多星座模式训练完成！")
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
