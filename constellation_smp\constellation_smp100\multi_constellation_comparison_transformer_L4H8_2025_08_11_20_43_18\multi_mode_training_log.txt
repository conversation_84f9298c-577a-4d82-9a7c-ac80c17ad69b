多星座模式训练实验
================================================================================
实验时间: 2025_08_11_20_43_18
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小32
使用Transformer: True
Transformer配置: 4层, 8头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 5,835,785
  Critic参数数量: 494,285
  总参数数量: 6,330,070
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0004, Critic=0.0004
  批次大小: 32
  训练轮数: 3
  梯度裁剪: 2.0
  权重衰减: 1e-05
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-11 20:49:50
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/3125, loss: -2078.180, reward: 14.877, critic_reward: -1.335, revenue_rate: 0.3839, distance: 6.1450, memory: 0.0256, power: 0.1896, lr: 0.000400, took: 44.637s
[COOPERATIVE] Epoch 1, Batch 20/3125, loss: -24.156, reward: 13.135, critic_reward: 12.971, revenue_rate: 0.3451, distance: 5.8450, memory: 0.0821, power: 0.1743, lr: 0.000400, took: 38.244s
[COOPERATIVE] Epoch 1, Batch 30/3125, loss: 77.976, reward: 11.709, critic_reward: 12.823, revenue_rate: 0.3094, distance: 5.3528, memory: 0.1098, power: 0.1622, lr: 0.000400, took: 27.033s
[COOPERATIVE] Epoch 1, Batch 40/3125, loss: -157.963, reward: 14.226, critic_reward: 12.733, revenue_rate: 0.3781, distance: 6.7230, memory: 0.1466, power: 0.2044, lr: 0.000400, took: 38.380s
[COOPERATIVE] Epoch 1, Batch 50/3125, loss: 0.891, reward: 14.847, critic_reward: 15.086, revenue_rate: 0.3901, distance: 6.7107, memory: 0.1115, power: 0.2017, lr: 0.000400, took: 42.687s
[COOPERATIVE] Epoch 1, Batch 60/3125, loss: 45.446, reward: 11.144, critic_reward: 11.787, revenue_rate: 0.2887, distance: 4.6528, memory: 0.0352, power: 0.1430, lr: 0.000400, took: 23.892s
[COOPERATIVE] Epoch 1, Batch 70/3125, loss: 61.696, reward: 10.612, critic_reward: 11.193, revenue_rate: 0.2750, distance: 4.4655, memory: 0.0512, power: 0.1353, lr: 0.000400, took: 22.468s
[COOPERATIVE] Epoch 1, Batch 80/3125, loss: 4.846, reward: 10.404, critic_reward: 10.525, revenue_rate: 0.2706, distance: 4.4442, memory: 0.0446, power: 0.1350, lr: 0.000400, took: 22.426s
[COOPERATIVE] Epoch 1, Batch 90/3125, loss: -36.686, reward: 9.326, critic_reward: 8.974, revenue_rate: 0.2439, distance: 4.1576, memory: 0.0677, power: 0.1266, lr: 0.000400, took: 20.901s
[COOPERATIVE] Epoch 1, Batch 100/3125, loss: -19.665, reward: 9.155, critic_reward: 9.009, revenue_rate: 0.2391, distance: 4.0953, memory: 0.0769, power: 0.1250, lr: 0.000400, took: 20.839s
[COOPERATIVE] Epoch 1, Batch 110/3125, loss: 14.021, reward: 9.860, critic_reward: 10.037, revenue_rate: 0.2613, distance: 4.6891, memory: 0.0730, power: 0.1411, lr: 0.000400, took: 23.683s
[COOPERATIVE] Epoch 1, Batch 120/3125, loss: -68.036, reward: 14.933, critic_reward: 14.466, revenue_rate: 0.4036, distance: 7.6897, memory: 0.1688, power: 0.2358, lr: 0.000400, took: 47.521s
[COOPERATIVE] Epoch 1, Batch 130/3125, loss: -27.592, reward: 16.645, critic_reward: 16.522, revenue_rate: 0.4512, distance: 8.8954, memory: 0.2033, power: 0.2671, lr: 0.000400, took: 61.132s
[COOPERATIVE] Epoch 1, Batch 140/3125, loss: 15.595, reward: 17.793, critic_reward: 17.927, revenue_rate: 0.4794, distance: 8.8104, memory: 0.1985, power: 0.2672, lr: 0.000400, took: 72.484s
[COOPERATIVE] Epoch 1, Batch 150/3125, loss: 282.117, reward: 17.553, critic_reward: 19.851, revenue_rate: 0.4707, distance: 8.8912, memory: 0.1928, power: 0.2657, lr: 0.000400, took: 69.933s
[COOPERATIVE] Epoch 1, Batch 160/3125, loss: -76.215, reward: 14.323, critic_reward: 14.093, revenue_rate: 0.3826, distance: 7.0387, memory: 0.1574, power: 0.2144, lr: 0.000400, took: 54.303s
[COOPERATIVE] Epoch 1, Batch 170/3125, loss: 33.582, reward: 12.328, critic_reward: 12.911, revenue_rate: 0.3270, distance: 5.9661, memory: 0.1154, power: 0.1800, lr: 0.000400, took: 35.860s
[COOPERATIVE] Epoch 1, Batch 180/3125, loss: 20.588, reward: 9.291, critic_reward: 9.757, revenue_rate: 0.2469, distance: 4.3733, memory: 0.0834, power: 0.1322, lr: 0.000400, took: 23.675s
[COOPERATIVE] Epoch 1, Batch 190/3125, loss: -25.081, reward: 9.119, critic_reward: 8.808, revenue_rate: 0.2418, distance: 4.3340, memory: 0.0765, power: 0.1305, lr: 0.000400, took: 23.433s
[COOPERATIVE] Epoch 1, Batch 200/3125, loss: -43.884, reward: 10.545, critic_reward: 10.235, revenue_rate: 0.2851, distance: 5.4260, memory: 0.1106, power: 0.1620, lr: 0.000400, took: 29.486s
[COOPERATIVE] Epoch 1, Batch 210/3125, loss: -162.547, reward: 14.517, critic_reward: 13.337, revenue_rate: 0.3900, distance: 7.4085, memory: 0.1452, power: 0.2257, lr: 0.000400, took: 56.735s
[COOPERATIVE] Epoch 1, Batch 220/3125, loss: 217.637, reward: 16.542, critic_reward: 18.253, revenue_rate: 0.4432, distance: 8.2010, memory: 0.1599, power: 0.2502, lr: 0.000400, took: 59.707s
[COOPERATIVE] Epoch 1, Batch 230/3125, loss: 172.546, reward: 16.880, critic_reward: 18.200, revenue_rate: 0.4586, distance: 8.8528, memory: 0.1931, power: 0.2652, lr: 0.000400, took: 67.520s
[COOPERATIVE] Epoch 1, Batch 240/3125, loss: 167.281, reward: 15.130, critic_reward: 16.888, revenue_rate: 0.4059, distance: 7.4232, memory: 0.1555, power: 0.2247, lr: 0.000400, took: 50.979s
[COOPERATIVE] Epoch 1, Batch 250/3125, loss: 96.512, reward: 11.642, critic_reward: 12.550, revenue_rate: 0.3027, distance: 5.0572, memory: 0.0622, power: 0.1532, lr: 0.000400, took: 27.407s
[COOPERATIVE] Epoch 1, Batch 260/3125, loss: -62.900, reward: 11.503, critic_reward: 11.071, revenue_rate: 0.2964, distance: 4.7928, memory: 0.0380, power: 0.1446, lr: 0.000400, took: 25.653s
[COOPERATIVE] Epoch 1, Batch 270/3125, loss: -30.138, reward: 11.590, critic_reward: 11.459, revenue_rate: 0.2990, distance: 4.8116, memory: 0.0407, power: 0.1452, lr: 0.000400, took: 25.936s
[COOPERATIVE] Epoch 1, Batch 280/3125, loss: -1.505, reward: 10.566, critic_reward: 10.653, revenue_rate: 0.2743, distance: 4.5180, memory: 0.0511, power: 0.1356, lr: 0.000400, took: 24.464s
[COOPERATIVE] Epoch 1, Batch 290/3125, loss: -92.679, reward: 11.160, critic_reward: 10.361, revenue_rate: 0.2889, distance: 4.6699, memory: 0.0325, power: 0.1399, lr: 0.000400, took: 25.181s
[COOPERATIVE] Epoch 1, Batch 300/3125, loss: 80.378, reward: 10.802, critic_reward: 11.636, revenue_rate: 0.2794, distance: 4.5211, memory: 0.0417, power: 0.1359, lr: 0.000400, took: 24.270s
[COOPERATIVE] Epoch 1, Batch 310/3125, loss: -84.951, reward: 11.398, critic_reward: 10.710, revenue_rate: 0.2941, distance: 4.7695, memory: 0.0436, power: 0.1449, lr: 0.000400, took: 25.647s
[COOPERATIVE] Epoch 1, Batch 320/3125, loss: -9.654, reward: 11.478, critic_reward: 11.467, revenue_rate: 0.2968, distance: 4.7156, memory: 0.0316, power: 0.1438, lr: 0.000400, took: 25.372s
[COOPERATIVE] Epoch 1, Batch 330/3125, loss: 57.531, reward: 11.222, critic_reward: 11.897, revenue_rate: 0.2901, distance: 4.6603, memory: 0.0319, power: 0.1407, lr: 0.000400, took: 25.197s
[COOPERATIVE] Epoch 1, Batch 340/3125, loss: -15.841, reward: 11.044, critic_reward: 10.965, revenue_rate: 0.2863, distance: 4.6951, memory: 0.0380, power: 0.1417, lr: 0.000400, took: 24.865s
[COOPERATIVE] Epoch 1, Batch 350/3125, loss: -29.554, reward: 11.021, critic_reward: 10.839, revenue_rate: 0.2861, distance: 4.6324, memory: 0.0386, power: 0.1386, lr: 0.000400, took: 24.995s
[COOPERATIVE] Epoch 1, Batch 360/3125, loss: 37.811, reward: 10.644, critic_reward: 11.095, revenue_rate: 0.2769, distance: 4.5617, memory: 0.0467, power: 0.1369, lr: 0.000400, took: 24.577s
[COOPERATIVE] Epoch 1, Batch 370/3125, loss: -27.910, reward: 10.607, critic_reward: 10.346, revenue_rate: 0.2717, distance: 4.2865, memory: 0.0302, power: 0.1338, lr: 0.000400, took: 23.523s
[COOPERATIVE] Epoch 1, Batch 380/3125, loss: 5.499, reward: 10.840, critic_reward: 10.911, revenue_rate: 0.2811, distance: 4.5420, memory: 0.0432, power: 0.1387, lr: 0.000400, took: 24.619s
[COOPERATIVE] Epoch 1, Batch 390/3125, loss: -26.872, reward: 11.083, critic_reward: 10.907, revenue_rate: 0.2886, distance: 4.6799, memory: 0.0563, power: 0.1423, lr: 0.000400, took: 25.113s
[COOPERATIVE] Epoch 1, Batch 400/3125, loss: 28.137, reward: 10.551, critic_reward: 10.860, revenue_rate: 0.2728, distance: 4.4459, memory: 0.0536, power: 0.1364, lr: 0.000400, took: 24.062s
[COOPERATIVE] Epoch 1, Batch 410/3125, loss: -27.912, reward: 10.720, critic_reward: 10.503, revenue_rate: 0.2787, distance: 4.6022, memory: 0.0659, power: 0.1433, lr: 0.000400, took: 24.747s
[COOPERATIVE] Epoch 1, Batch 420/3125, loss: 23.090, reward: 10.186, critic_reward: 10.578, revenue_rate: 0.2642, distance: 4.3559, memory: 0.0526, power: 0.1317, lr: 0.000400, took: 23.333s
[COOPERATIVE] Epoch 1, Batch 430/3125, loss: -30.993, reward: 10.147, critic_reward: 10.017, revenue_rate: 0.2654, distance: 4.5208, memory: 0.0730, power: 0.1362, lr: 0.000400, took: 24.404s
[COOPERATIVE] Epoch 1, Batch 440/3125, loss: -50.959, reward: 9.510, critic_reward: 9.066, revenue_rate: 0.2499, distance: 4.3669, memory: 0.0833, power: 0.1357, lr: 0.000400, took: 23.960s
[COOPERATIVE] Epoch 1, Batch 450/3125, loss: -223.681, reward: 11.582, critic_reward: 9.492, revenue_rate: 0.3126, distance: 5.9133, memory: 0.1148, power: 0.1775, lr: 0.000400, took: 36.404s
[COOPERATIVE] Epoch 1, Batch 460/3125, loss: 30.297, reward: 13.628, critic_reward: 13.947, revenue_rate: 0.3768, distance: 7.7743, memory: 0.1655, power: 0.2334, lr: 0.000400, took: 53.516s
[COOPERATIVE] Epoch 1, Batch 470/3125, loss: -377.919, reward: 16.427, critic_reward: 13.947, revenue_rate: 0.4500, distance: 8.8850, memory: 0.1929, power: 0.2703, lr: 0.000400, took: 69.864s
[COOPERATIVE] Epoch 1, Batch 480/3125, loss: 516.630, reward: 14.404, critic_reward: 18.738, revenue_rate: 0.3934, distance: 7.8144, memory: 0.1775, power: 0.2334, lr: 0.000400, took: 58.436s
[COOPERATIVE] Epoch 1, Batch 490/3125, loss: 141.941, reward: 11.613, critic_reward: 12.709, revenue_rate: 0.3076, distance: 5.4910, memory: 0.1214, power: 0.1658, lr: 0.000400, took: 29.887s
[COOPERATIVE] Epoch 1, Batch 500/3125, loss: -11.672, reward: 14.343, critic_reward: 14.237, revenue_rate: 0.3920, distance: 7.8458, memory: 0.1575, power: 0.2344, lr: 0.000400, took: 55.267s
[COOPERATIVE] Epoch 1, Batch 510/3125, loss: -243.574, reward: 15.561, critic_reward: 13.907, revenue_rate: 0.4246, distance: 8.4338, memory: 0.1830, power: 0.2564, lr: 0.000400, took: 62.907s
[COOPERATIVE] Epoch 1, Batch 520/3125, loss: -204.470, reward: 17.926, critic_reward: 16.563, revenue_rate: 0.4872, distance: 9.6535, memory: 0.2169, power: 0.2913, lr: 0.000400, took: 74.943s
[COOPERATIVE] Epoch 1, Batch 530/3125, loss: 206.912, reward: 17.463, critic_reward: 19.221, revenue_rate: 0.4732, distance: 8.9702, memory: 0.2142, power: 0.2763, lr: 0.000400, took: 73.855s
[COOPERATIVE] Epoch 1, Batch 540/3125, loss: -189.550, reward: 16.875, critic_reward: 15.503, revenue_rate: 0.4578, distance: 8.7860, memory: 0.2028, power: 0.2665, lr: 0.000400, took: 70.539s
[COOPERATIVE] Epoch 1, Batch 550/3125, loss: 159.663, reward: 14.722, critic_reward: 16.189, revenue_rate: 0.4077, distance: 8.5125, memory: 0.2095, power: 0.2595, lr: 0.000400, took: 64.134s
[COOPERATIVE] Epoch 1, Batch 560/3125, loss: -151.396, reward: 15.458, critic_reward: 14.646, revenue_rate: 0.4222, distance: 8.3747, memory: 0.1863, power: 0.2550, lr: 0.000400, took: 57.200s
[COOPERATIVE] Epoch 1, Batch 570/3125, loss: 119.152, reward: 14.665, critic_reward: 15.656, revenue_rate: 0.4021, distance: 8.0779, memory: 0.1656, power: 0.2436, lr: 0.000400, took: 59.460s
[COOPERATIVE] Epoch 1, Batch 580/3125, loss: 51.136, reward: 14.226, critic_reward: 14.841, revenue_rate: 0.3805, distance: 7.1442, memory: 0.1443, power: 0.2174, lr: 0.000400, took: 50.322s
[COOPERATIVE] Epoch 1, Batch 590/3125, loss: -245.195, reward: 12.835, critic_reward: 11.116, revenue_rate: 0.3421, distance: 6.0452, memory: 0.1341, power: 0.1825, lr: 0.000400, took: 38.301s
[COOPERATIVE] Epoch 1, Batch 600/3125, loss: -643.006, reward: 15.769, critic_reward: 10.862, revenue_rate: 0.4240, distance: 8.1350, memory: 0.1871, power: 0.2453, lr: 0.000400, took: 60.471s
[COOPERATIVE] Epoch 1, Batch 610/3125, loss: 58.635, reward: 11.905, critic_reward: 12.517, revenue_rate: 0.3108, distance: 5.2474, memory: 0.0541, power: 0.1579, lr: 0.000400, took: 28.549s
[COOPERATIVE] Epoch 1, Batch 620/3125, loss: -264.973, reward: 14.600, critic_reward: 12.528, revenue_rate: 0.3993, distance: 8.0666, memory: 0.1768, power: 0.2450, lr: 0.000400, took: 60.868s
[COOPERATIVE] Epoch 1, Batch 630/3125, loss: -212.974, reward: 15.112, critic_reward: 13.612, revenue_rate: 0.4136, distance: 8.3451, memory: 0.2026, power: 0.2523, lr: 0.000400, took: 63.945s
[COOPERATIVE] Epoch 1, Batch 640/3125, loss: 233.872, reward: 12.676, critic_reward: 14.796, revenue_rate: 0.3510, distance: 7.2529, memory: 0.1656, power: 0.2223, lr: 0.000400, took: 58.082s
[COOPERATIVE] Epoch 1, Batch 650/3125, loss: 99.995, reward: 12.579, critic_reward: 13.786, revenue_rate: 0.3500, distance: 7.5502, memory: 0.1598, power: 0.2291, lr: 0.000400, took: 56.988s
[COOPERATIVE] Epoch 1, Batch 660/3125, loss: -158.214, reward: 14.015, critic_reward: 12.723, revenue_rate: 0.3911, distance: 8.5251, memory: 0.1975, power: 0.2572, lr: 0.000400, took: 64.731s
[COOPERATIVE] Epoch 1, Batch 670/3125, loss: -77.261, reward: 13.934, critic_reward: 13.404, revenue_rate: 0.3855, distance: 7.9205, memory: 0.1639, power: 0.2371, lr: 0.000400, took: 60.405s
[COOPERATIVE] Epoch 1, Batch 680/3125, loss: -48.093, reward: 14.245, critic_reward: 14.015, revenue_rate: 0.3868, distance: 7.5411, memory: 0.1329, power: 0.2284, lr: 0.000400, took: 57.294s
[COOPERATIVE] Epoch 1, Batch 690/3125, loss: 192.546, reward: 11.941, critic_reward: 13.912, revenue_rate: 0.3316, distance: 7.0802, memory: 0.1570, power: 0.2147, lr: 0.000400, took: 55.028s
[COOPERATIVE] Epoch 1, Batch 700/3125, loss: 75.705, reward: 11.433, critic_reward: 12.353, revenue_rate: 0.3187, distance: 6.7518, memory: 0.1394, power: 0.2042, lr: 0.000400, took: 50.242s
[COOPERATIVE] Epoch 1, Batch 710/3125, loss: -244.590, reward: 13.003, critic_reward: 11.217, revenue_rate: 0.3665, distance: 7.9279, memory: 0.1843, power: 0.2421, lr: 0.000400, took: 65.672s
[COOPERATIVE] Epoch 1, Batch 720/3125, loss: -95.717, reward: 12.941, critic_reward: 12.152, revenue_rate: 0.3647, distance: 8.1945, memory: 0.1712, power: 0.2493, lr: 0.000400, took: 58.345s
[COOPERATIVE] Epoch 1, Batch 730/3125, loss: 5.118, reward: 13.056, critic_reward: 13.245, revenue_rate: 0.3706, distance: 8.4224, memory: 0.1965, power: 0.2561, lr: 0.000400, took: 63.650s
[COOPERATIVE] Epoch 1, Batch 740/3125, loss: -90.078, reward: 14.020, critic_reward: 13.509, revenue_rate: 0.3933, distance: 8.4887, memory: 0.1997, power: 0.2594, lr: 0.000400, took: 62.706s
[COOPERATIVE] Epoch 1, Batch 750/3125, loss: -96.676, reward: 14.751, critic_reward: 14.173, revenue_rate: 0.4101, distance: 8.7746, memory: 0.1871, power: 0.2624, lr: 0.000400, took: 67.093s
[COOPERATIVE] Epoch 1, Batch 760/3125, loss: 185.016, reward: 12.688, critic_reward: 14.479, revenue_rate: 0.3570, distance: 7.7308, memory: 0.1717, power: 0.2354, lr: 0.000400, took: 57.932s
[COOPERATIVE] Epoch 1, Batch 770/3125, loss: -58.882, reward: 13.744, critic_reward: 13.467, revenue_rate: 0.3908, distance: 8.7711, memory: 0.2093, power: 0.2635, lr: 0.000400, took: 66.169s
[COOPERATIVE] Epoch 1, Batch 780/3125, loss: 61.778, reward: 12.055, critic_reward: 12.795, revenue_rate: 0.3449, distance: 7.9554, memory: 0.1904, power: 0.2402, lr: 0.000400, took: 62.224s
[COOPERATIVE] Epoch 1, Batch 790/3125, loss: -85.943, reward: 12.899, critic_reward: 12.336, revenue_rate: 0.3668, distance: 8.3531, memory: 0.1748, power: 0.2533, lr: 0.000400, took: 65.099s
[COOPERATIVE] Epoch 1, Batch 800/3125, loss: -104.628, reward: 13.608, critic_reward: 12.849, revenue_rate: 0.3852, distance: 8.5836, memory: 0.1781, power: 0.2636, lr: 0.000400, took: 66.251s
[COOPERATIVE] Epoch 1, Batch 810/3125, loss: -82.001, reward: 14.180, critic_reward: 13.642, revenue_rate: 0.3983, distance: 8.6479, memory: 0.1877, power: 0.2615, lr: 0.000400, took: 64.327s
[COOPERATIVE] Epoch 1, Batch 820/3125, loss: -1.205, reward: 14.560, critic_reward: 14.685, revenue_rate: 0.4087, distance: 9.0832, memory: 0.2294, power: 0.2762, lr: 0.000400, took: 76.968s
[COOPERATIVE] Epoch 1, Batch 830/3125, loss: 192.551, reward: 12.319, critic_reward: 14.532, revenue_rate: 0.3514, distance: 8.2063, memory: 0.1861, power: 0.2459, lr: 0.000400, took: 61.619s
[COOPERATIVE] Epoch 1, Batch 840/3125, loss: 44.248, reward: 12.407, critic_reward: 13.128, revenue_rate: 0.3524, distance: 8.0818, memory: 0.1791, power: 0.2406, lr: 0.000400, took: 59.351s
[COOPERATIVE] Epoch 1, Batch 850/3125, loss: -37.489, reward: 12.280, critic_reward: 12.012, revenue_rate: 0.3535, distance: 8.2655, memory: 0.2044, power: 0.2539, lr: 0.000400, took: 62.678s
[COOPERATIVE] Epoch 1, Batch 860/3125, loss: -107.227, reward: 13.080, critic_reward: 12.159, revenue_rate: 0.3763, distance: 8.8273, memory: 0.2139, power: 0.2664, lr: 0.000400, took: 68.804s
[COOPERATIVE] Epoch 1, Batch 870/3125, loss: -19.336, reward: 13.158, critic_reward: 13.122, revenue_rate: 0.3772, distance: 8.7973, memory: 0.2189, power: 0.2700, lr: 0.000400, took: 72.869s
[COOPERATIVE] Epoch 1, Batch 880/3125, loss: 32.792, reward: 13.154, critic_reward: 13.721, revenue_rate: 0.3776, distance: 8.8891, memory: 0.2002, power: 0.2696, lr: 0.000400, took: 70.498s
[COOPERATIVE] Epoch 1, Batch 890/3125, loss: 70.173, reward: 12.237, critic_reward: 13.257, revenue_rate: 0.3518, distance: 8.1634, memory: 0.1676, power: 0.2439, lr: 0.000400, took: 58.005s
[COOPERATIVE] Epoch 1, Batch 900/3125, loss: 96.976, reward: 10.520, critic_reward: 11.849, revenue_rate: 0.3085, distance: 7.5204, memory: 0.1731, power: 0.2279, lr: 0.000400, took: 57.851s
[COOPERATIVE] Epoch 1, Batch 910/3125, loss: -31.134, reward: 10.519, critic_reward: 10.341, revenue_rate: 0.3042, distance: 7.3300, memory: 0.1667, power: 0.2229, lr: 0.000400, took: 55.698s
[COOPERATIVE] Epoch 1, Batch 920/3125, loss: -65.818, reward: 10.733, critic_reward: 10.196, revenue_rate: 0.3064, distance: 7.0477, memory: 0.1643, power: 0.2203, lr: 0.000400, took: 56.466s
[COOPERATIVE] Epoch 1, Batch 930/3125, loss: -31.260, reward: 10.550, critic_reward: 10.424, revenue_rate: 0.3060, distance: 7.3061, memory: 0.1587, power: 0.2228, lr: 0.000400, took: 57.630s
[COOPERATIVE] Epoch 1, Batch 940/3125, loss: -236.251, reward: 13.271, critic_reward: 11.052, revenue_rate: 0.3796, distance: 8.8228, memory: 0.2078, power: 0.2664, lr: 0.000400, took: 68.633s
[COOPERATIVE] Epoch 1, Batch 950/3125, loss: -68.642, reward: 13.172, critic_reward: 12.657, revenue_rate: 0.3794, distance: 8.8314, memory: 0.1992, power: 0.2644, lr: 0.000400, took: 67.102s
[COOPERATIVE] Epoch 1, Batch 960/3125, loss: -79.841, reward: 14.640, critic_reward: 14.078, revenue_rate: 0.4133, distance: 9.2598, memory: 0.2330, power: 0.2809, lr: 0.000400, took: 73.798s
[COOPERATIVE] Epoch 1, Batch 970/3125, loss: 82.278, reward: 13.663, critic_reward: 14.722, revenue_rate: 0.3884, distance: 8.8733, memory: 0.2082, power: 0.2693, lr: 0.000400, took: 73.200s
[COOPERATIVE] Epoch 1, Batch 980/3125, loss: 37.687, reward: 13.155, critic_reward: 13.767, revenue_rate: 0.3778, distance: 8.7458, memory: 0.2147, power: 0.2663, lr: 0.000400, took: 68.775s
[COOPERATIVE] Epoch 1, Batch 990/3125, loss: -31.778, reward: 12.652, critic_reward: 12.611, revenue_rate: 0.3668, distance: 8.7380, memory: 0.2040, power: 0.2636, lr: 0.000400, took: 67.848s
[COOPERATIVE] Epoch 1, Batch 1000/3125, loss: -219.816, reward: 14.327, critic_reward: 12.586, revenue_rate: 0.4089, distance: 9.4054, memory: 0.2048, power: 0.2825, lr: 0.000400, took: 76.476s
[COOPERATIVE] Epoch 1, Batch 1010/3125, loss: -83.175, reward: 14.443, critic_reward: 13.865, revenue_rate: 0.4136, distance: 9.5353, memory: 0.2264, power: 0.2889, lr: 0.000400, took: 78.876s
[COOPERATIVE] Epoch 1, Batch 1020/3125, loss: 60.035, reward: 13.978, critic_reward: 14.581, revenue_rate: 0.4001, distance: 9.2874, memory: 0.2266, power: 0.2804, lr: 0.000400, took: 77.016s
[COOPERATIVE] Epoch 1, Batch 1030/3125, loss: -0.650, reward: 13.907, critic_reward: 14.041, revenue_rate: 0.4024, distance: 9.3242, memory: 0.2557, power: 0.2824, lr: 0.000400, took: 76.502s
[COOPERATIVE] Epoch 1, Batch 1040/3125, loss: -118.545, reward: 14.838, critic_reward: 13.966, revenue_rate: 0.4221, distance: 9.5986, memory: 0.2402, power: 0.2925, lr: 0.000400, took: 77.130s
[COOPERATIVE] Epoch 1, Batch 1050/3125, loss: 24.498, reward: 14.330, critic_reward: 14.768, revenue_rate: 0.4093, distance: 9.5092, memory: 0.2251, power: 0.2851, lr: 0.000400, took: 77.829s
[COOPERATIVE] Epoch 1, Batch 1060/3125, loss: 166.225, reward: 12.660, critic_reward: 14.377, revenue_rate: 0.3636, distance: 8.4054, memory: 0.1875, power: 0.2540, lr: 0.000400, took: 65.217s
[COOPERATIVE] Epoch 1, Batch 1070/3125, loss: 26.914, reward: 12.461, critic_reward: 13.004, revenue_rate: 0.3602, distance: 8.6003, memory: 0.1782, power: 0.2591, lr: 0.000400, took: 67.461s
[COOPERATIVE] Epoch 1, Batch 1080/3125, loss: -47.370, reward: 12.276, critic_reward: 11.891, revenue_rate: 0.3534, distance: 8.3967, memory: 0.2090, power: 0.2530, lr: 0.000400, took: 63.908s
[COOPERATIVE] Epoch 1, Batch 1090/3125, loss: -68.734, reward: 12.784, critic_reward: 12.076, revenue_rate: 0.3711, distance: 8.7087, memory: 0.2203, power: 0.2611, lr: 0.000400, took: 68.716s
[COOPERATIVE] Epoch 1, Batch 1100/3125, loss: -217.936, reward: 15.168, critic_reward: 13.409, revenue_rate: 0.4279, distance: 9.4663, memory: 0.2330, power: 0.2874, lr: 0.000400, took: 75.933s
[COOPERATIVE] Epoch 1, Batch 1110/3125, loss: 59.864, reward: 13.765, critic_reward: 14.668, revenue_rate: 0.3942, distance: 9.0907, memory: 0.2154, power: 0.2731, lr: 0.000400, took: 72.595s
[COOPERATIVE] Epoch 1, Batch 1120/3125, loss: 111.950, reward: 13.025, critic_reward: 14.246, revenue_rate: 0.3718, distance: 8.6723, memory: 0.2072, power: 0.2628, lr: 0.000400, took: 67.661s
[COOPERATIVE] Epoch 1, Batch 1130/3125, loss: -31.593, reward: 13.331, critic_reward: 13.201, revenue_rate: 0.3839, distance: 8.8977, memory: 0.2142, power: 0.2689, lr: 0.000400, took: 70.065s
[COOPERATIVE] Epoch 1, Batch 1140/3125, loss: -231.798, reward: 14.930, critic_reward: 13.046, revenue_rate: 0.4230, distance: 9.5608, memory: 0.2349, power: 0.2864, lr: 0.000400, took: 78.051s
[COOPERATIVE] Epoch 1, Batch 1150/3125, loss: -327.018, reward: 16.279, critic_reward: 14.282, revenue_rate: 0.4533, distance: 9.4898, memory: 0.2425, power: 0.2864, lr: 0.000400, took: 77.636s
[COOPERATIVE] Epoch 1, Batch 1160/3125, loss: -160.677, reward: 17.244, critic_reward: 15.953, revenue_rate: 0.4776, distance: 10.0229, memory: 0.2518, power: 0.3029, lr: 0.000400, took: 72.588s
[COOPERATIVE] Epoch 1, Batch 1170/3125, loss: 25.070, reward: 17.206, critic_reward: 17.608, revenue_rate: 0.4805, distance: 10.2302, memory: 0.2480, power: 0.3088, lr: 0.000400, took: 76.057s
[COOPERATIVE] Epoch 1, Batch 1180/3125, loss: 106.251, reward: 16.409, critic_reward: 17.643, revenue_rate: 0.4597, distance: 9.8748, memory: 0.2256, power: 0.2993, lr: 0.000400, took: 82.152s
[COOPERATIVE] Epoch 1, Batch 1190/3125, loss: 13.077, reward: 16.394, critic_reward: 16.727, revenue_rate: 0.4622, distance: 10.1335, memory: 0.2426, power: 0.3056, lr: 0.000400, took: 77.656s
[COOPERATIVE] Epoch 1, Batch 1200/3125, loss: -24.294, reward: 16.496, critic_reward: 16.419, revenue_rate: 0.4621, distance: 10.1086, memory: 0.2248, power: 0.3114, lr: 0.000400, took: 82.200s
[COOPERATIVE] Epoch 1, Batch 1210/3125, loss: 20.404, reward: 15.543, critic_reward: 15.841, revenue_rate: 0.4406, distance: 9.9948, memory: 0.2349, power: 0.3009, lr: 0.000400, took: 81.100s
[COOPERATIVE] Epoch 1, Batch 1220/3125, loss: -32.197, reward: 15.307, critic_reward: 15.240, revenue_rate: 0.4337, distance: 9.6284, memory: 0.2467, power: 0.2947, lr: 0.000400, took: 76.198s
[COOPERATIVE] Epoch 1, Batch 1230/3125, loss: 13.601, reward: 15.020, critic_reward: 15.277, revenue_rate: 0.4241, distance: 9.5256, memory: 0.2381, power: 0.2897, lr: 0.000400, took: 78.204s
[COOPERATIVE] Epoch 1, Batch 1240/3125, loss: 4.894, reward: 14.912, critic_reward: 15.207, revenue_rate: 0.4252, distance: 9.5424, memory: 0.2213, power: 0.2877, lr: 0.000400, took: 78.181s
[COOPERATIVE] Epoch 1, Batch 1250/3125, loss: 99.627, reward: 13.394, critic_reward: 14.764, revenue_rate: 0.3847, distance: 9.0391, memory: 0.2277, power: 0.2758, lr: 0.000400, took: 76.987s
[COOPERATIVE] Epoch 1, Batch 1260/3125, loss: -24.878, reward: 13.953, critic_reward: 13.810, revenue_rate: 0.4034, distance: 9.5357, memory: 0.2386, power: 0.2856, lr: 0.000400, took: 82.202s
[COOPERATIVE] Epoch 1, Batch 1270/3125, loss: -30.166, reward: 13.931, critic_reward: 13.812, revenue_rate: 0.3972, distance: 9.2523, memory: 0.2214, power: 0.2823, lr: 0.000400, took: 79.287s
[COOPERATIVE] Epoch 1, Batch 1280/3125, loss: 141.872, reward: 12.370, critic_reward: 13.862, revenue_rate: 0.3575, distance: 8.5140, memory: 0.1999, power: 0.2564, lr: 0.000400, took: 65.408s
[COOPERATIVE] Epoch 1, Batch 1290/3125, loss: -137.957, reward: 14.020, critic_reward: 13.113, revenue_rate: 0.3965, distance: 8.7984, memory: 0.1982, power: 0.2647, lr: 0.000400, took: 66.898s
[COOPERATIVE] Epoch 1, Batch 1300/3125, loss: -161.485, reward: 14.682, critic_reward: 13.423, revenue_rate: 0.4144, distance: 9.3026, memory: 0.2228, power: 0.2805, lr: 0.000400, took: 79.340s
[COOPERATIVE] Epoch 1, Batch 1310/3125, loss: 10.932, reward: 13.709, critic_reward: 14.111, revenue_rate: 0.3878, distance: 8.8765, memory: 0.2122, power: 0.2711, lr: 0.000400, took: 70.952s
[COOPERATIVE] Epoch 1, Batch 1320/3125, loss: 31.412, reward: 13.206, critic_reward: 13.833, revenue_rate: 0.3771, distance: 8.7346, memory: 0.2035, power: 0.2631, lr: 0.000400, took: 67.647s
[COOPERATIVE] Epoch 1, Batch 1330/3125, loss: -131.133, reward: 14.621, critic_reward: 13.656, revenue_rate: 0.4140, distance: 9.2510, memory: 0.2255, power: 0.2800, lr: 0.000400, took: 77.130s
[COOPERATIVE] Epoch 1, Batch 1340/3125, loss: -83.689, reward: 15.162, critic_reward: 14.590, revenue_rate: 0.4316, distance: 9.8584, memory: 0.2356, power: 0.2941, lr: 0.000400, took: 77.457s
[COOPERATIVE] Epoch 1, Batch 1350/3125, loss: -24.520, reward: 15.616, critic_reward: 15.668, revenue_rate: 0.4441, distance: 10.0073, memory: 0.2254, power: 0.3004, lr: 0.000400, took: 82.429s
[COOPERATIVE] Epoch 1, Batch 1360/3125, loss: 97.348, reward: 14.387, critic_reward: 15.528, revenue_rate: 0.4121, distance: 9.5898, memory: 0.2261, power: 0.2914, lr: 0.000400, took: 77.123s
[COOPERATIVE] Epoch 1, Batch 1370/3125, loss: 72.189, reward: 13.294, critic_reward: 14.105, revenue_rate: 0.3814, distance: 8.8322, memory: 0.2136, power: 0.2703, lr: 0.000400, took: 70.113s
[COOPERATIVE] Epoch 1, Batch 1380/3125, loss: 6.865, reward: 12.256, critic_reward: 12.526, revenue_rate: 0.3572, distance: 8.6656, memory: 0.1999, power: 0.2584, lr: 0.000400, took: 67.730s
[COOPERATIVE] Epoch 1, Batch 1390/3125, loss: -212.556, reward: 13.890, critic_reward: 11.979, revenue_rate: 0.3967, distance: 9.1974, memory: 0.2115, power: 0.2777, lr: 0.000400, took: 76.275s
[COOPERATIVE] Epoch 1, Batch 1400/3125, loss: -126.381, reward: 13.804, critic_reward: 12.973, revenue_rate: 0.3926, distance: 8.8898, memory: 0.2079, power: 0.2683, lr: 0.000400, took: 72.946s
[COOPERATIVE] Epoch 1, Batch 1410/3125, loss: -7.999, reward: 13.311, critic_reward: 13.440, revenue_rate: 0.3845, distance: 9.2129, memory: 0.2185, power: 0.2765, lr: 0.000400, took: 77.545s
[COOPERATIVE] Epoch 1, Batch 1420/3125, loss: 36.719, reward: 12.838, critic_reward: 13.437, revenue_rate: 0.3744, distance: 9.0737, memory: 0.2109, power: 0.2693, lr: 0.000400, took: 76.498s
[COOPERATIVE] Epoch 1, Batch 1430/3125, loss: -24.844, reward: 13.043, critic_reward: 12.898, revenue_rate: 0.3773, distance: 9.0791, memory: 0.2006, power: 0.2727, lr: 0.000400, took: 76.542s
[COOPERATIVE] Epoch 1, Batch 1440/3125, loss: -94.150, reward: 13.098, critic_reward: 12.483, revenue_rate: 0.3817, distance: 9.1705, memory: 0.2186, power: 0.2765, lr: 0.000400, took: 74.199s
[COOPERATIVE] Epoch 1, Batch 1450/3125, loss: -75.828, reward: 12.925, critic_reward: 12.641, revenue_rate: 0.3739, distance: 8.9417, memory: 0.2162, power: 0.2714, lr: 0.000400, took: 68.718s
[COOPERATIVE] Epoch 1, Batch 1460/3125, loss: 28.081, reward: 12.020, critic_reward: 12.520, revenue_rate: 0.3505, distance: 8.4065, memory: 0.1889, power: 0.2552, lr: 0.000400, took: 65.682s
[COOPERATIVE] Epoch 1, Batch 1470/3125, loss: 2.422, reward: 11.423, critic_reward: 11.720, revenue_rate: 0.3323, distance: 8.1285, memory: 0.1998, power: 0.2478, lr: 0.000400, took: 62.131s
[COOPERATIVE] Epoch 1, Batch 1480/3125, loss: -191.110, reward: 12.761, critic_reward: 11.129, revenue_rate: 0.3672, distance: 8.7261, memory: 0.2129, power: 0.2644, lr: 0.000400, took: 69.984s
[COOPERATIVE] Epoch 1, Batch 1490/3125, loss: -178.738, reward: 13.225, critic_reward: 11.931, revenue_rate: 0.3767, distance: 8.6671, memory: 0.1863, power: 0.2629, lr: 0.000400, took: 72.436s
[COOPERATIVE] Epoch 1, Batch 1500/3125, loss: 82.776, reward: 11.833, critic_reward: 13.006, revenue_rate: 0.3408, distance: 8.2580, memory: 0.1943, power: 0.2472, lr: 0.000400, took: 58.689s
[COOPERATIVE] Epoch 1, Batch 1510/3125, loss: 86.120, reward: 11.453, critic_reward: 12.660, revenue_rate: 0.3358, distance: 8.2165, memory: 0.2015, power: 0.2489, lr: 0.000400, took: 57.006s
[COOPERATIVE] Epoch 1, Batch 1520/3125, loss: 38.681, reward: 10.696, critic_reward: 11.386, revenue_rate: 0.3135, distance: 7.7956, memory: 0.1745, power: 0.2337, lr: 0.000400, took: 54.488s
[COOPERATIVE] Epoch 1, Batch 1530/3125, loss: 41.153, reward: 9.102, critic_reward: 9.902, revenue_rate: 0.2669, distance: 6.5213, memory: 0.1419, power: 0.2000, lr: 0.000400, took: 48.082s
[COOPERATIVE] Epoch 1, Batch 1540/3125, loss: -76.108, reward: 9.636, critic_reward: 8.876, revenue_rate: 0.2798, distance: 6.8131, memory: 0.1407, power: 0.2066, lr: 0.000400, took: 51.174s
[COOPERATIVE] Epoch 1, Batch 1550/3125, loss: -25.593, reward: 9.115, critic_reward: 8.960, revenue_rate: 0.2646, distance: 6.3848, memory: 0.1336, power: 0.1913, lr: 0.000400, took: 42.908s
[COOPERATIVE] Epoch 1, Batch 1560/3125, loss: -22.388, reward: 9.113, critic_reward: 9.033, revenue_rate: 0.2655, distance: 6.4483, memory: 0.1503, power: 0.1981, lr: 0.000400, took: 47.933s
[COOPERATIVE] Epoch 1, Batch 1570/3125, loss: -260.941, reward: 12.261, critic_reward: 9.393, revenue_rate: 0.3553, distance: 8.6027, memory: 0.2126, power: 0.2562, lr: 0.000400, took: 67.819s
[COOPERATIVE] Epoch 1, Batch 1580/3125, loss: -269.778, reward: 13.671, critic_reward: 10.785, revenue_rate: 0.3910, distance: 8.9923, memory: 0.2120, power: 0.2724, lr: 0.000400, took: 76.955s
[COOPERATIVE] Epoch 1, Batch 1590/3125, loss: -210.090, reward: 14.412, critic_reward: 12.608, revenue_rate: 0.4111, distance: 9.3534, memory: 0.2236, power: 0.2796, lr: 0.000400, took: 76.378s
[COOPERATIVE] Epoch 1, Batch 1600/3125, loss: -8.794, reward: 13.965, critic_reward: 14.016, revenue_rate: 0.4011, distance: 9.3841, memory: 0.2359, power: 0.2816, lr: 0.000400, took: 78.701s
[COOPERATIVE] Epoch 1, Batch 1610/3125, loss: 44.948, reward: 13.780, critic_reward: 14.378, revenue_rate: 0.3936, distance: 9.1194, memory: 0.2103, power: 0.2724, lr: 0.000400, took: 77.237s
[COOPERATIVE] Epoch 1, Batch 1620/3125, loss: 64.376, reward: 13.040, critic_reward: 13.737, revenue_rate: 0.3780, distance: 8.9447, memory: 0.2229, power: 0.2667, lr: 0.000400, took: 70.752s
[COOPERATIVE] Epoch 1, Batch 1630/3125, loss: -110.707, reward: 13.647, critic_reward: 12.747, revenue_rate: 0.3935, distance: 9.2228, memory: 0.2296, power: 0.2812, lr: 0.000400, took: 75.481s
[COOPERATIVE] Epoch 1, Batch 1640/3125, loss: -29.838, reward: 12.901, critic_reward: 12.821, revenue_rate: 0.3744, distance: 8.9136, memory: 0.2223, power: 0.2702, lr: 0.000400, took: 75.123s
[COOPERATIVE] Epoch 1, Batch 1650/3125, loss: 30.083, reward: 12.320, critic_reward: 12.827, revenue_rate: 0.3608, distance: 8.7042, memory: 0.2082, power: 0.2650, lr: 0.000400, took: 69.434s
[COOPERATIVE] Epoch 1, Batch 1660/3125, loss: 63.999, reward: 11.588, critic_reward: 12.332, revenue_rate: 0.3367, distance: 8.1430, memory: 0.1942, power: 0.2452, lr: 0.000400, took: 55.663s
[COOPERATIVE] Epoch 1, Batch 1670/3125, loss: -91.096, reward: 11.858, critic_reward: 11.222, revenue_rate: 0.3474, distance: 8.5815, memory: 0.1986, power: 0.2549, lr: 0.000400, took: 63.930s
[COOPERATIVE] Epoch 1, Batch 1680/3125, loss: -79.181, reward: 11.688, critic_reward: 10.950, revenue_rate: 0.3385, distance: 8.2064, memory: 0.1997, power: 0.2506, lr: 0.000400, took: 57.655s
[COOPERATIVE] Epoch 1, Batch 1690/3125, loss: -62.844, reward: 12.309, critic_reward: 11.878, revenue_rate: 0.3555, distance: 8.4040, memory: 0.1996, power: 0.2527, lr: 0.000400, took: 65.059s
[COOPERATIVE] Epoch 1, Batch 1700/3125, loss: -70.836, reward: 12.859, critic_reward: 12.367, revenue_rate: 0.3740, distance: 8.8349, memory: 0.2118, power: 0.2692, lr: 0.000400, took: 71.534s
[COOPERATIVE] Epoch 1, Batch 1710/3125, loss: -83.679, reward: 13.290, critic_reward: 12.843, revenue_rate: 0.3790, distance: 8.6092, memory: 0.1814, power: 0.2634, lr: 0.000400, took: 69.183s
[COOPERATIVE] Epoch 1, Batch 1720/3125, loss: -62.482, reward: 13.824, critic_reward: 13.401, revenue_rate: 0.3949, distance: 8.9535, memory: 0.2023, power: 0.2738, lr: 0.000400, took: 74.815s
[COOPERATIVE] Epoch 1, Batch 1730/3125, loss: -42.685, reward: 14.126, critic_reward: 13.892, revenue_rate: 0.4018, distance: 9.0320, memory: 0.2070, power: 0.2705, lr: 0.000400, took: 73.370s
[COOPERATIVE] Epoch 1, Batch 1740/3125, loss: -38.768, reward: 14.303, critic_reward: 14.213, revenue_rate: 0.4090, distance: 9.5008, memory: 0.2343, power: 0.2894, lr: 0.000400, took: 79.334s
[COOPERATIVE] Epoch 1, Batch 1750/3125, loss: -29.441, reward: 14.643, critic_reward: 14.478, revenue_rate: 0.4172, distance: 9.6050, memory: 0.2288, power: 0.2886, lr: 0.000400, took: 82.932s
[COOPERATIVE] Epoch 1, Batch 1760/3125, loss: 70.714, reward: 13.878, critic_reward: 14.805, revenue_rate: 0.3986, distance: 9.4174, memory: 0.2272, power: 0.2824, lr: 0.000400, took: 78.669s
[COOPERATIVE] Epoch 1, Batch 1770/3125, loss: 153.481, reward: 12.342, critic_reward: 14.368, revenue_rate: 0.3561, distance: 8.4940, memory: 0.2036, power: 0.2598, lr: 0.000400, took: 66.652s
[COOPERATIVE] Epoch 1, Batch 1780/3125, loss: 145.606, reward: 11.219, critic_reward: 13.095, revenue_rate: 0.3229, distance: 7.6756, memory: 0.1845, power: 0.2336, lr: 0.000400, took: 55.543s
[COOPERATIVE] Epoch 1, Batch 1790/3125, loss: 8.340, reward: 11.377, critic_reward: 11.585, revenue_rate: 0.3250, distance: 7.5562, memory: 0.1667, power: 0.2288, lr: 0.000400, took: 57.156s
[COOPERATIVE] Epoch 1, Batch 1800/3125, loss: -107.895, reward: 12.145, critic_reward: 11.146, revenue_rate: 0.3442, distance: 7.7965, memory: 0.1850, power: 0.2374, lr: 0.000400, took: 55.828s
[COOPERATIVE] Epoch 1, Batch 1810/3125, loss: -74.707, reward: 12.444, critic_reward: 11.849, revenue_rate: 0.3538, distance: 8.0104, memory: 0.1826, power: 0.2406, lr: 0.000400, took: 60.948s
[COOPERATIVE] Epoch 1, Batch 1820/3125, loss: -57.849, reward: 12.949, critic_reward: 12.601, revenue_rate: 0.3718, distance: 8.7551, memory: 0.2033, power: 0.2659, lr: 0.000400, took: 70.978s
[COOPERATIVE] Epoch 1, Batch 1830/3125, loss: 32.068, reward: 12.470, critic_reward: 13.050, revenue_rate: 0.3652, distance: 8.9889, memory: 0.2036, power: 0.2659, lr: 0.000400, took: 71.431s
[COOPERATIVE] Epoch 1, Batch 1840/3125, loss: -97.096, reward: 13.593, critic_reward: 12.791, revenue_rate: 0.3889, distance: 9.0547, memory: 0.2105, power: 0.2729, lr: 0.000400, took: 74.682s
[COOPERATIVE] Epoch 1, Batch 1850/3125, loss: -57.726, reward: 13.320, critic_reward: 13.091, revenue_rate: 0.3797, distance: 8.5702, memory: 0.1959, power: 0.2602, lr: 0.000400, took: 69.138s
[COOPERATIVE] Epoch 1, Batch 1860/3125, loss: 98.490, reward: 12.225, critic_reward: 13.591, revenue_rate: 0.3528, distance: 8.4344, memory: 0.2012, power: 0.2535, lr: 0.000400, took: 65.101s
[COOPERATIVE] Epoch 1, Batch 1870/3125, loss: 102.926, reward: 11.279, critic_reward: 12.835, revenue_rate: 0.3297, distance: 8.2214, memory: 0.1909, power: 0.2440, lr: 0.000400, took: 58.970s
[COOPERATIVE] Epoch 1, Batch 1880/3125, loss: 2.785, reward: 10.938, critic_reward: 11.315, revenue_rate: 0.3239, distance: 8.1578, memory: 0.2036, power: 0.2431, lr: 0.000400, took: 60.232s
[COOPERATIVE] Epoch 1, Batch 1890/3125, loss: -43.924, reward: 10.601, critic_reward: 10.411, revenue_rate: 0.3095, distance: 7.6593, memory: 0.1726, power: 0.2329, lr: 0.000400, took: 58.994s
[COOPERATIVE] Epoch 1, Batch 1900/3125, loss: -54.748, reward: 10.521, critic_reward: 10.210, revenue_rate: 0.3110, distance: 7.6147, memory: 0.1681, power: 0.2314, lr: 0.000400, took: 54.983s
[COOPERATIVE] Epoch 1, Batch 1910/3125, loss: -72.875, reward: 10.879, critic_reward: 10.260, revenue_rate: 0.3184, distance: 7.8204, memory: 0.1863, power: 0.2345, lr: 0.000400, took: 57.653s
[COOPERATIVE] Epoch 1, Batch 1920/3125, loss: -83.405, reward: 11.289, critic_reward: 10.598, revenue_rate: 0.3306, distance: 8.1116, memory: 0.2007, power: 0.2461, lr: 0.000400, took: 60.102s
[COOPERATIVE] Epoch 1, Batch 1930/3125, loss: -112.803, reward: 12.750, critic_reward: 11.810, revenue_rate: 0.3671, distance: 8.6961, memory: 0.2187, power: 0.2666, lr: 0.000400, took: 72.376s
[COOPERATIVE] Epoch 1, Batch 1940/3125, loss: 4.179, reward: 12.626, critic_reward: 12.869, revenue_rate: 0.3665, distance: 8.9420, memory: 0.2085, power: 0.2704, lr: 0.000400, took: 72.894s
[COOPERATIVE] Epoch 1, Batch 1950/3125, loss: 36.884, reward: 12.293, critic_reward: 12.949, revenue_rate: 0.3614, distance: 8.9020, memory: 0.2078, power: 0.2655, lr: 0.000400, took: 70.428s
[COOPERATIVE] Epoch 1, Batch 1960/3125, loss: -9.170, reward: 12.406, critic_reward: 12.496, revenue_rate: 0.3612, distance: 8.6813, memory: 0.2189, power: 0.2628, lr: 0.000400, took: 71.807s
[COOPERATIVE] Epoch 1, Batch 1970/3125, loss: -67.032, reward: 12.613, critic_reward: 12.258, revenue_rate: 0.3679, distance: 8.9428, memory: 0.2153, power: 0.2684, lr: 0.000400, took: 71.603s
[COOPERATIVE] Epoch 1, Batch 1980/3125, loss: -45.229, reward: 12.357, critic_reward: 11.991, revenue_rate: 0.3581, distance: 8.7801, memory: 0.2153, power: 0.2657, lr: 0.000400, took: 69.980s
[COOPERATIVE] Epoch 1, Batch 1990/3125, loss: -149.231, reward: 13.135, critic_reward: 12.090, revenue_rate: 0.3773, distance: 8.7020, memory: 0.1885, power: 0.2617, lr: 0.000400, took: 64.929s
[COOPERATIVE] Epoch 1, Batch 2000/3125, loss: -102.577, reward: 13.718, critic_reward: 12.945, revenue_rate: 0.3891, distance: 8.8814, memory: 0.1920, power: 0.2641, lr: 0.000400, took: 71.087s
[COOPERATIVE] Epoch 1, Batch 2010/3125, loss: 19.324, reward: 13.386, critic_reward: 13.783, revenue_rate: 0.3860, distance: 9.0412, memory: 0.2254, power: 0.2745, lr: 0.000400, took: 75.480s
[COOPERATIVE] Epoch 1, Batch 2020/3125, loss: -128.430, reward: 15.113, critic_reward: 13.888, revenue_rate: 0.4294, distance: 9.7698, memory: 0.2359, power: 0.2935, lr: 0.000400, took: 82.590s
[COOPERATIVE] Epoch 1, Batch 2030/3125, loss: -78.650, reward: 15.327, critic_reward: 14.827, revenue_rate: 0.4326, distance: 9.7093, memory: 0.2247, power: 0.2917, lr: 0.000400, took: 80.714s
[COOPERATIVE] Epoch 1, Batch 2040/3125, loss: 24.596, reward: 14.928, critic_reward: 15.329, revenue_rate: 0.4257, distance: 9.7297, memory: 0.2408, power: 0.2929, lr: 0.000400, took: 82.562s
[COOPERATIVE] Epoch 1, Batch 2050/3125, loss: 5.708, reward: 14.518, critic_reward: 14.744, revenue_rate: 0.4160, distance: 9.5984, memory: 0.2298, power: 0.2884, lr: 0.000400, took: 81.715s
[COOPERATIVE] Epoch 1, Batch 2060/3125, loss: 39.956, reward: 13.506, critic_reward: 14.187, revenue_rate: 0.3915, distance: 9.2640, memory: 0.2196, power: 0.2754, lr: 0.000400, took: 78.687s
[COOPERATIVE] Epoch 1, Batch 2070/3125, loss: -106.213, reward: 14.302, critic_reward: 13.538, revenue_rate: 0.4110, distance: 9.6146, memory: 0.2401, power: 0.2922, lr: 0.000400, took: 80.195s
[COOPERATIVE] Epoch 1, Batch 2080/3125, loss: -36.119, reward: 14.085, critic_reward: 13.903, revenue_rate: 0.4070, distance: 9.5851, memory: 0.2245, power: 0.2858, lr: 0.000400, took: 82.438s
[COOPERATIVE] Epoch 1, Batch 2090/3125, loss: 36.910, reward: 13.630, critic_reward: 14.117, revenue_rate: 0.3931, distance: 9.2074, memory: 0.2339, power: 0.2791, lr: 0.000400, took: 77.953s
[COOPERATIVE] Epoch 1, Batch 2100/3125, loss: -10.154, reward: 13.712, critic_reward: 13.764, revenue_rate: 0.3938, distance: 9.2064, memory: 0.2152, power: 0.2785, lr: 0.000400, took: 74.345s
[COOPERATIVE] Epoch 1, Batch 2110/3125, loss: -28.385, reward: 13.635, critic_reward: 13.613, revenue_rate: 0.3923, distance: 9.1751, memory: 0.2251, power: 0.2822, lr: 0.000400, took: 74.554s
[COOPERATIVE] Epoch 1, Batch 2120/3125, loss: -29.716, reward: 13.315, critic_reward: 13.300, revenue_rate: 0.3832, distance: 8.8618, memory: 0.2044, power: 0.2690, lr: 0.000400, took: 74.840s
[COOPERATIVE] Epoch 1, Batch 2130/3125, loss: -36.661, reward: 13.464, critic_reward: 13.293, revenue_rate: 0.3875, distance: 9.0598, memory: 0.1924, power: 0.2715, lr: 0.000400, took: 73.493s
[COOPERATIVE] Epoch 1, Batch 2140/3125, loss: -16.603, reward: 13.292, critic_reward: 13.233, revenue_rate: 0.3837, distance: 9.0839, memory: 0.2270, power: 0.2751, lr: 0.000400, took: 77.020s
[COOPERATIVE] Epoch 1, Batch 2150/3125, loss: -140.335, reward: 14.602, critic_reward: 13.428, revenue_rate: 0.4200, distance: 9.7995, memory: 0.2429, power: 0.2945, lr: 0.000400, took: 79.479s
[COOPERATIVE] Epoch 1, Batch 2160/3125, loss: -36.807, reward: 14.448, critic_reward: 14.205, revenue_rate: 0.4182, distance: 9.7541, memory: 0.2389, power: 0.2959, lr: 0.000400, took: 82.541s
[COOPERATIVE] Epoch 1, Batch 2170/3125, loss: 3.984, reward: 14.096, critic_reward: 14.378, revenue_rate: 0.4065, distance: 9.6264, memory: 0.2285, power: 0.2928, lr: 0.000400, took: 83.862s
[COOPERATIVE] Epoch 1, Batch 2180/3125, loss: -20.516, reward: 14.005, critic_reward: 14.058, revenue_rate: 0.4049, distance: 9.6027, memory: 0.2437, power: 0.2886, lr: 0.000400, took: 79.704s
[COOPERATIVE] Epoch 1, Batch 2190/3125, loss: -5.727, reward: 13.844, critic_reward: 13.895, revenue_rate: 0.3996, distance: 9.4429, memory: 0.2329, power: 0.2855, lr: 0.000400, took: 82.141s
[COOPERATIVE] Epoch 1, Batch 2200/3125, loss: 17.963, reward: 13.421, critic_reward: 13.694, revenue_rate: 0.3875, distance: 9.2143, memory: 0.2148, power: 0.2759, lr: 0.000400, took: 75.939s
[COOPERATIVE] Epoch 1, Batch 2210/3125, loss: 1.811, reward: 12.888, critic_reward: 13.030, revenue_rate: 0.3735, distance: 9.0072, memory: 0.2152, power: 0.2721, lr: 0.000400, took: 73.220s
[COOPERATIVE] Epoch 1, Batch 2220/3125, loss: 6.480, reward: 12.262, critic_reward: 12.459, revenue_rate: 0.3581, distance: 8.7320, memory: 0.2037, power: 0.2632, lr: 0.000400, took: 71.068s
[COOPERATIVE] Epoch 1, Batch 2230/3125, loss: -16.837, reward: 11.836, critic_reward: 11.776, revenue_rate: 0.3494, distance: 8.6506, memory: 0.2164, power: 0.2616, lr: 0.000400, took: 70.067s
[COOPERATIVE] Epoch 1, Batch 2240/3125, loss: -136.338, reward: 12.746, critic_reward: 11.387, revenue_rate: 0.3710, distance: 9.0608, memory: 0.2209, power: 0.2725, lr: 0.000400, took: 75.568s
[COOPERATIVE] Epoch 1, Batch 2250/3125, loss: 76.211, reward: 10.783, critic_reward: 12.094, revenue_rate: 0.3119, distance: 7.4656, memory: 0.1753, power: 0.2292, lr: 0.000400, took: 55.126s
[COOPERATIVE] Epoch 1, Batch 2260/3125, loss: 180.966, reward: 9.008, critic_reward: 11.605, revenue_rate: 0.2587, distance: 5.9885, memory: 0.1419, power: 0.1801, lr: 0.000400, took: 34.693s
[COOPERATIVE] Epoch 1, Batch 2270/3125, loss: 15.735, reward: 9.864, critic_reward: 10.277, revenue_rate: 0.2901, distance: 7.0677, memory: 0.1646, power: 0.2145, lr: 0.000400, took: 54.577s
[COOPERATIVE] Epoch 1, Batch 2280/3125, loss: -139.927, reward: 11.165, critic_reward: 9.761, revenue_rate: 0.3282, distance: 8.1745, memory: 0.2013, power: 0.2465, lr: 0.000400, took: 56.556s
[COOPERATIVE] Epoch 1, Batch 2290/3125, loss: -148.744, reward: 12.053, critic_reward: 10.753, revenue_rate: 0.3521, distance: 8.5102, memory: 0.2097, power: 0.2570, lr: 0.000400, took: 63.913s
[COOPERATIVE] Epoch 1, Batch 2300/3125, loss: -65.283, reward: 12.789, critic_reward: 12.307, revenue_rate: 0.3695, distance: 8.8312, memory: 0.2006, power: 0.2678, lr: 0.000400, took: 72.699s
[COOPERATIVE] Epoch 1, Batch 2310/3125, loss: 2.246, reward: 13.099, critic_reward: 13.462, revenue_rate: 0.3795, distance: 9.0127, memory: 0.2198, power: 0.2721, lr: 0.000400, took: 74.484s
[COOPERATIVE] Epoch 1, Batch 2320/3125, loss: 102.003, reward: 11.876, critic_reward: 13.366, revenue_rate: 0.3443, distance: 8.2467, memory: 0.1884, power: 0.2461, lr: 0.000400, took: 61.524s
[COOPERATIVE] Epoch 1, Batch 2330/3125, loss: -4.360, reward: 12.306, critic_reward: 12.415, revenue_rate: 0.3601, distance: 8.8062, memory: 0.2085, power: 0.2674, lr: 0.000400, took: 72.369s
[COOPERATIVE] Epoch 1, Batch 2340/3125, loss: -86.819, reward: 12.725, critic_reward: 11.944, revenue_rate: 0.3681, distance: 8.9188, memory: 0.2166, power: 0.2666, lr: 0.000400, took: 72.525s
[COOPERATIVE] Epoch 1, Batch 2350/3125, loss: -34.033, reward: 12.475, critic_reward: 12.367, revenue_rate: 0.3603, distance: 8.5388, memory: 0.1991, power: 0.2580, lr: 0.000400, took: 67.168s
[COOPERATIVE] Epoch 1, Batch 2360/3125, loss: -37.772, reward: 12.935, critic_reward: 12.770, revenue_rate: 0.3730, distance: 8.8981, memory: 0.2066, power: 0.2703, lr: 0.000400, took: 70.990s
[COOPERATIVE] Epoch 1, Batch 2370/3125, loss: -12.945, reward: 13.561, critic_reward: 13.489, revenue_rate: 0.3888, distance: 8.9617, memory: 0.2169, power: 0.2712, lr: 0.000400, took: 75.814s
[COOPERATIVE] Epoch 1, Batch 2380/3125, loss: -94.065, reward: 14.951, critic_reward: 14.226, revenue_rate: 0.4240, distance: 9.6294, memory: 0.2221, power: 0.2902, lr: 0.000400, took: 77.362s
[COOPERATIVE] Epoch 1, Batch 2390/3125, loss: 54.340, reward: 13.613, critic_reward: 14.543, revenue_rate: 0.3919, distance: 9.2415, memory: 0.2308, power: 0.2781, lr: 0.000400, took: 77.635s
[COOPERATIVE] Epoch 1, Batch 2400/3125, loss: -53.753, reward: 14.554, critic_reward: 14.133, revenue_rate: 0.4162, distance: 9.6653, memory: 0.2452, power: 0.2922, lr: 0.000400, took: 80.525s
[COOPERATIVE] Epoch 1, Batch 2410/3125, loss: -120.295, reward: 15.043, critic_reward: 14.211, revenue_rate: 0.4236, distance: 9.4060, memory: 0.2307, power: 0.2868, lr: 0.000400, took: 77.926s
[COOPERATIVE] Epoch 1, Batch 2420/3125, loss: -45.548, reward: 15.182, critic_reward: 14.883, revenue_rate: 0.4349, distance: 9.9002, memory: 0.2442, power: 0.2963, lr: 0.000400, took: 81.476s
[COOPERATIVE] Epoch 1, Batch 2430/3125, loss: 151.468, reward: 13.512, critic_reward: 15.190, revenue_rate: 0.3873, distance: 9.0283, memory: 0.2230, power: 0.2687, lr: 0.000400, took: 75.742s
[COOPERATIVE] Epoch 1, Batch 2440/3125, loss: -36.554, reward: 14.381, critic_reward: 14.306, revenue_rate: 0.4124, distance: 9.5944, memory: 0.2286, power: 0.2911, lr: 0.000400, took: 79.005s
[COOPERATIVE] Epoch 1, Batch 2450/3125, loss: -99.518, reward: 14.808, critic_reward: 14.000, revenue_rate: 0.4253, distance: 9.8016, memory: 0.2495, power: 0.2997, lr: 0.000400, took: 84.691s
[COOPERATIVE] Epoch 1, Batch 2460/3125, loss: -87.629, reward: 15.279, critic_reward: 14.734, revenue_rate: 0.4372, distance: 10.0197, memory: 0.2462, power: 0.3015, lr: 0.000400, took: 79.525s
[COOPERATIVE] Epoch 1, Batch 2470/3125, loss: 7.841, reward: 15.013, critic_reward: 15.206, revenue_rate: 0.4327, distance: 10.0309, memory: 0.2557, power: 0.3025, lr: 0.000400, took: 76.450s
[COOPERATIVE] Epoch 1, Batch 2480/3125, loss: -5.137, reward: 14.685, critic_reward: 15.004, revenue_rate: 0.4197, distance: 9.7327, memory: 0.2314, power: 0.2930, lr: 0.000400, took: 77.337s
[COOPERATIVE] Epoch 1, Batch 2490/3125, loss: 12.976, reward: 14.316, critic_reward: 14.643, revenue_rate: 0.4113, distance: 9.6958, memory: 0.2353, power: 0.2899, lr: 0.000400, took: 80.309s
[COOPERATIVE] Epoch 1, Batch 2500/3125, loss: -91.200, reward: 14.707, critic_reward: 14.112, revenue_rate: 0.4220, distance: 9.8462, memory: 0.2472, power: 0.2984, lr: 0.000400, took: 80.118s
[COOPERATIVE] Epoch 1, Batch 2510/3125, loss: -193.738, reward: 15.533, critic_reward: 14.149, revenue_rate: 0.4414, distance: 9.9159, memory: 0.2318, power: 0.3001, lr: 0.000400, took: 81.752s
[COOPERATIVE] Epoch 1, Batch 2520/3125, loss: -129.525, reward: 15.922, critic_reward: 15.106, revenue_rate: 0.4500, distance: 10.0237, memory: 0.2340, power: 0.3032, lr: 0.000400, took: 82.534s
[COOPERATIVE] Epoch 1, Batch 2530/3125, loss: 53.749, reward: 15.257, critic_reward: 16.041, revenue_rate: 0.4375, distance: 10.1053, memory: 0.2158, power: 0.3027, lr: 0.000400, took: 75.254s
[COOPERATIVE] Epoch 1, Batch 2540/3125, loss: 213.991, reward: 13.249, critic_reward: 15.902, revenue_rate: 0.3878, distance: 9.4328, memory: 0.2193, power: 0.2857, lr: 0.000400, took: 80.197s
[COOPERATIVE] Epoch 1, Batch 2550/3125, loss: 96.308, reward: 13.426, critic_reward: 14.560, revenue_rate: 0.3876, distance: 9.3620, memory: 0.2254, power: 0.2818, lr: 0.000400, took: 78.643s
[COOPERATIVE] Epoch 1, Batch 2560/3125, loss: 119.389, reward: 11.819, critic_reward: 13.170, revenue_rate: 0.3433, distance: 8.4496, memory: 0.2005, power: 0.2541, lr: 0.000400, took: 68.090s
[COOPERATIVE] Epoch 1, Batch 2570/3125, loss: 38.429, reward: 11.474, critic_reward: 12.053, revenue_rate: 0.3348, distance: 8.1896, memory: 0.2012, power: 0.2492, lr: 0.000400, took: 61.421s
[COOPERATIVE] Epoch 1, Batch 2580/3125, loss: -59.505, reward: 11.319, critic_reward: 11.036, revenue_rate: 0.3314, distance: 8.1453, memory: 0.1824, power: 0.2463, lr: 0.000400, took: 63.607s
[COOPERATIVE] Epoch 1, Batch 2590/3125, loss: -177.814, reward: 12.570, critic_reward: 11.010, revenue_rate: 0.3670, distance: 8.9709, memory: 0.2186, power: 0.2726, lr: 0.000400, took: 73.933s
[COOPERATIVE] Epoch 1, Batch 2600/3125, loss: -108.044, reward: 13.267, critic_reward: 12.269, revenue_rate: 0.3851, distance: 9.2670, memory: 0.2270, power: 0.2797, lr: 0.000400, took: 78.589s
[COOPERATIVE] Epoch 1, Batch 2610/3125, loss: -84.601, reward: 14.603, critic_reward: 13.940, revenue_rate: 0.4202, distance: 9.7617, memory: 0.2303, power: 0.2959, lr: 0.000400, took: 82.232s
[COOPERATIVE] Epoch 1, Batch 2620/3125, loss: -47.968, reward: 15.464, critic_reward: 15.142, revenue_rate: 0.4423, distance: 10.0960, memory: 0.2578, power: 0.3084, lr: 0.000400, took: 77.315s
[COOPERATIVE] Epoch 1, Batch 2630/3125, loss: -20.670, reward: 15.885, critic_reward: 15.901, revenue_rate: 0.4509, distance: 10.3011, memory: 0.2577, power: 0.3123, lr: 0.000400, took: 79.406s
[COOPERATIVE] Epoch 1, Batch 2640/3125, loss: -26.649, reward: 16.132, critic_reward: 15.983, revenue_rate: 0.4622, distance: 10.5309, memory: 0.2676, power: 0.3178, lr: 0.000400, took: 79.887s
[COOPERATIVE] Epoch 1, Batch 2650/3125, loss: -80.039, reward: 16.495, critic_reward: 15.893, revenue_rate: 0.4689, distance: 10.4885, memory: 0.2540, power: 0.3155, lr: 0.000400, took: 78.103s
[COOPERATIVE] Epoch 1, Batch 2660/3125, loss: 16.763, reward: 16.285, critic_reward: 16.640, revenue_rate: 0.4607, distance: 10.1496, memory: 0.2437, power: 0.3059, lr: 0.000400, took: 76.837s
[COOPERATIVE] Epoch 1, Batch 2670/3125, loss: 48.549, reward: 16.080, critic_reward: 16.677, revenue_rate: 0.4550, distance: 10.1678, memory: 0.2539, power: 0.3027, lr: 0.000400, took: 78.434s
[COOPERATIVE] Epoch 1, Batch 2680/3125, loss: -41.812, reward: 16.399, critic_reward: 16.152, revenue_rate: 0.4603, distance: 10.0881, memory: 0.2405, power: 0.3048, lr: 0.000400, took: 80.428s
[COOPERATIVE] Epoch 1, Batch 2690/3125, loss: 26.774, reward: 15.619, critic_reward: 16.053, revenue_rate: 0.4452, distance: 10.1288, memory: 0.2476, power: 0.3051, lr: 0.000400, took: 77.830s
[COOPERATIVE] Epoch 1, Batch 2700/3125, loss: 24.723, reward: 14.858, critic_reward: 15.358, revenue_rate: 0.4293, distance: 10.1405, memory: 0.2488, power: 0.3021, lr: 0.000400, took: 77.651s
[COOPERATIVE] Epoch 1, Batch 2710/3125, loss: 24.569, reward: 13.896, critic_reward: 14.346, revenue_rate: 0.4022, distance: 9.5720, memory: 0.2305, power: 0.2880, lr: 0.000400, took: 83.445s
[COOPERATIVE] Epoch 1, Batch 2720/3125, loss: -186.075, reward: 15.271, critic_reward: 13.776, revenue_rate: 0.4371, distance: 10.1165, memory: 0.2382, power: 0.3064, lr: 0.000400, took: 79.201s
[COOPERATIVE] Epoch 1, Batch 2730/3125, loss: -40.645, reward: 14.747, critic_reward: 14.491, revenue_rate: 0.4173, distance: 9.4386, memory: 0.2065, power: 0.2829, lr: 0.000400, took: 72.962s
[COOPERATIVE] Epoch 1, Batch 2740/3125, loss: 16.877, reward: 14.473, critic_reward: 14.850, revenue_rate: 0.4088, distance: 9.3219, memory: 0.2136, power: 0.2817, lr: 0.000400, took: 78.146s
[COOPERATIVE] Epoch 1, Batch 2750/3125, loss: 8.505, reward: 14.518, critic_reward: 14.867, revenue_rate: 0.4127, distance: 9.4155, memory: 0.2103, power: 0.2850, lr: 0.000400, took: 78.932s
[COOPERATIVE] Epoch 1, Batch 2760/3125, loss: 172.160, reward: 13.026, critic_reward: 14.627, revenue_rate: 0.3732, distance: 8.6160, memory: 0.2074, power: 0.2610, lr: 0.000400, took: 65.527s
[COOPERATIVE] Epoch 1, Batch 2770/3125, loss: 0.538, reward: 13.252, critic_reward: 13.465, revenue_rate: 0.3837, distance: 9.0886, memory: 0.1999, power: 0.2741, lr: 0.000400, took: 72.714s
[COOPERATIVE] Epoch 1, Batch 2780/3125, loss: -52.119, reward: 13.034, critic_reward: 12.773, revenue_rate: 0.3792, distance: 9.1635, memory: 0.2114, power: 0.2770, lr: 0.000400, took: 77.701s
[COOPERATIVE] Epoch 1, Batch 2790/3125, loss: -83.898, reward: 13.502, critic_reward: 12.779, revenue_rate: 0.3923, distance: 9.4459, memory: 0.2183, power: 0.2828, lr: 0.000400, took: 75.087s
[COOPERATIVE] Epoch 1, Batch 2800/3125, loss: 17.331, reward: 12.746, critic_reward: 13.087, revenue_rate: 0.3735, distance: 9.0838, memory: 0.2425, power: 0.2766, lr: 0.000400, took: 78.154s
[COOPERATIVE] Epoch 1, Batch 2810/3125, loss: -13.079, reward: 12.708, critic_reward: 12.881, revenue_rate: 0.3663, distance: 8.5841, memory: 0.1985, power: 0.2609, lr: 0.000400, took: 64.058s
[COOPERATIVE] Epoch 1, Batch 2820/3125, loss: 48.577, reward: 11.749, critic_reward: 12.503, revenue_rate: 0.3412, distance: 8.0590, memory: 0.1979, power: 0.2411, lr: 0.000400, took: 58.478s
[COOPERATIVE] Epoch 1, Batch 2830/3125, loss: -70.715, reward: 12.331, critic_reward: 11.947, revenue_rate: 0.3503, distance: 7.9449, memory: 0.1856, power: 0.2412, lr: 0.000400, took: 58.548s
[COOPERATIVE] Epoch 1, Batch 2840/3125, loss: -40.041, reward: 11.958, critic_reward: 11.721, revenue_rate: 0.3407, distance: 7.7830, memory: 0.1726, power: 0.2349, lr: 0.000400, took: 56.219s
[COOPERATIVE] Epoch 1, Batch 2850/3125, loss: 9.076, reward: 11.391, critic_reward: 11.833, revenue_rate: 0.3274, distance: 7.6812, memory: 0.1907, power: 0.2325, lr: 0.000400, took: 55.916s
[COOPERATIVE] Epoch 1, Batch 2860/3125, loss: -38.616, reward: 11.990, critic_reward: 11.692, revenue_rate: 0.3497, distance: 8.4051, memory: 0.2038, power: 0.2536, lr: 0.000400, took: 61.044s
[COOPERATIVE] Epoch 1, Batch 2870/3125, loss: -260.918, reward: 14.354, critic_reward: 12.075, revenue_rate: 0.4140, distance: 9.5494, memory: 0.2374, power: 0.2886, lr: 0.000400, took: 78.885s
[COOPERATIVE] Epoch 1, Batch 2880/3125, loss: -170.479, reward: 14.750, critic_reward: 13.440, revenue_rate: 0.4179, distance: 9.4277, memory: 0.2507, power: 0.2885, lr: 0.000400, took: 80.982s
[COOPERATIVE] Epoch 1, Batch 2890/3125, loss: -83.200, reward: 15.065, critic_reward: 14.585, revenue_rate: 0.4302, distance: 9.7759, memory: 0.2402, power: 0.2932, lr: 0.000400, took: 82.019s
[COOPERATIVE] Epoch 1, Batch 2900/3125, loss: 73.668, reward: 14.324, critic_reward: 15.159, revenue_rate: 0.4087, distance: 9.5671, memory: 0.2382, power: 0.2931, lr: 0.000400, took: 82.830s
[COOPERATIVE] Epoch 1, Batch 2910/3125, loss: 81.580, reward: 13.363, critic_reward: 14.347, revenue_rate: 0.3859, distance: 9.1998, memory: 0.2153, power: 0.2797, lr: 0.000400, took: 79.638s
[COOPERATIVE] Epoch 1, Batch 2920/3125, loss: -40.279, reward: 13.242, critic_reward: 13.014, revenue_rate: 0.3850, distance: 9.1968, memory: 0.2242, power: 0.2787, lr: 0.000400, took: 73.336s
[COOPERATIVE] Epoch 1, Batch 2930/3125, loss: -130.838, reward: 14.027, critic_reward: 12.881, revenue_rate: 0.4084, distance: 9.6234, memory: 0.2488, power: 0.2925, lr: 0.000400, took: 84.798s
[COOPERATIVE] Epoch 1, Batch 2940/3125, loss: -94.792, reward: 14.852, critic_reward: 14.030, revenue_rate: 0.4278, distance: 10.0486, memory: 0.2450, power: 0.3020, lr: 0.000400, took: 80.631s
[COOPERATIVE] Epoch 1, Batch 2950/3125, loss: 2.326, reward: 14.916, critic_reward: 15.237, revenue_rate: 0.4316, distance: 10.1859, memory: 0.2497, power: 0.3084, lr: 0.000400, took: 86.139s
[COOPERATIVE] Epoch 1, Batch 2960/3125, loss: 43.452, reward: 14.109, critic_reward: 15.036, revenue_rate: 0.4085, distance: 9.7790, memory: 0.2328, power: 0.2928, lr: 0.000400, took: 80.600s
[COOPERATIVE] Epoch 1, Batch 2970/3125, loss: -15.139, reward: 14.228, critic_reward: 14.253, revenue_rate: 0.4120, distance: 9.8045, memory: 0.2520, power: 0.2982, lr: 0.000400, took: 83.496s
[COOPERATIVE] Epoch 1, Batch 2980/3125, loss: -98.050, reward: 14.902, critic_reward: 14.079, revenue_rate: 0.4264, distance: 9.8840, memory: 0.2284, power: 0.2998, lr: 0.000400, took: 81.897s
[COOPERATIVE] Epoch 1, Batch 2990/3125, loss: 58.795, reward: 13.834, critic_reward: 14.652, revenue_rate: 0.4003, distance: 9.4897, memory: 0.2363, power: 0.2889, lr: 0.000400, took: 80.659s
[COOPERATIVE] Epoch 1, Batch 3000/3125, loss: 52.650, reward: 13.178, critic_reward: 14.024, revenue_rate: 0.3809, distance: 9.0954, memory: 0.2130, power: 0.2741, lr: 0.000400, took: 73.632s
[COOPERATIVE] Epoch 1, Batch 3010/3125, loss: 17.901, reward: 12.571, critic_reward: 12.977, revenue_rate: 0.3660, distance: 8.7188, memory: 0.2083, power: 0.2618, lr: 0.000400, took: 67.360s
[COOPERATIVE] Epoch 1, Batch 3020/3125, loss: 6.560, reward: 11.923, critic_reward: 12.174, revenue_rate: 0.3484, distance: 8.4951, memory: 0.2144, power: 0.2575, lr: 0.000400, took: 66.757s
[COOPERATIVE] Epoch 1, Batch 3030/3125, loss: -194.903, reward: 13.660, critic_reward: 11.880, revenue_rate: 0.3933, distance: 9.3212, memory: 0.2283, power: 0.2844, lr: 0.000400, took: 78.602s
[COOPERATIVE] Epoch 1, Batch 3040/3125, loss: -120.531, reward: 13.920, critic_reward: 12.868, revenue_rate: 0.4002, distance: 9.3156, memory: 0.2240, power: 0.2840, lr: 0.000400, took: 83.134s
[COOPERATIVE] Epoch 1, Batch 3050/3125, loss: 23.401, reward: 13.436, critic_reward: 13.783, revenue_rate: 0.3877, distance: 9.1429, memory: 0.2231, power: 0.2742, lr: 0.000400, took: 76.763s
[COOPERATIVE] Epoch 1, Batch 3060/3125, loss: 65.341, reward: 12.796, critic_reward: 13.701, revenue_rate: 0.3723, distance: 9.0032, memory: 0.2105, power: 0.2713, lr: 0.000400, took: 73.567s
[COOPERATIVE] Epoch 1, Batch 3070/3125, loss: -44.050, reward: 13.322, critic_reward: 12.945, revenue_rate: 0.3871, distance: 9.2212, memory: 0.2241, power: 0.2797, lr: 0.000400, took: 76.810s
[COOPERATIVE] Epoch 1, Batch 3080/3125, loss: -7.787, reward: 13.082, critic_reward: 13.080, revenue_rate: 0.3745, distance: 8.7090, memory: 0.2112, power: 0.2634, lr: 0.000400, took: 70.935s
[COOPERATIVE] Epoch 1, Batch 3090/3125, loss: 65.206, reward: 12.311, critic_reward: 13.017, revenue_rate: 0.3564, distance: 8.4190, memory: 0.1967, power: 0.2537, lr: 0.000400, took: 62.688s
[COOPERATIVE] Epoch 1, Batch 3100/3125, loss: 87.294, reward: 11.480, critic_reward: 12.415, revenue_rate: 0.3349, distance: 8.0494, memory: 0.1957, power: 0.2435, lr: 0.000400, took: 55.774s
[COOPERATIVE] Epoch 1, Batch 3110/3125, loss: 10.523, reward: 10.776, critic_reward: 11.349, revenue_rate: 0.3138, distance: 7.6552, memory: 0.1680, power: 0.2327, lr: 0.000400, took: 56.281s
[COOPERATIVE] Epoch 1, Batch 3120/3125, loss: -76.989, reward: 11.303, critic_reward: 10.737, revenue_rate: 0.3300, distance: 7.9473, memory: 0.1820, power: 0.2412, lr: 0.000400, took: 61.227s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 12.869, revenue_rate: 0.3587, distance: 7.6066, memory: 0.1727, power: 0.2299
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L4H8_2025_08_11_20_43_18\constellation_gpnindrnn_cooperative_transformer_L4H8_2025_08_11_20_43_18 (验证集奖励: 12.8693)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/3125, loss: -190.794, reward: 12.925, critic_reward: 11.570, revenue_rate: 0.3633, distance: 7.9704, memory: 0.1759, power: 0.2409, lr: 0.000400, took: 52.947s
[COOPERATIVE] Epoch 2, Batch 20/3125, loss: -115.223, reward: 13.806, critic_reward: 12.972, revenue_rate: 0.3915, distance: 8.9417, memory: 0.2065, power: 0.2683, lr: 0.000400, took: 67.001s
[COOPERATIVE] Epoch 2, Batch 30/3125, loss: -12.768, reward: 14.248, critic_reward: 14.186, revenue_rate: 0.4064, distance: 9.4645, memory: 0.2431, power: 0.2881, lr: 0.000400, took: 76.803s
[COOPERATIVE] Epoch 2, Batch 40/3125, loss: 47.957, reward: 14.154, critic_reward: 14.732, revenue_rate: 0.4026, distance: 9.2016, memory: 0.2077, power: 0.2768, lr: 0.000400, took: 69.915s
[COOPERATIVE] Epoch 2, Batch 50/3125, loss: 75.989, reward: 13.452, critic_reward: 14.237, revenue_rate: 0.3890, distance: 9.1132, memory: 0.2156, power: 0.2761, lr: 0.000400, took: 66.552s
[COOPERATIVE] Epoch 2, Batch 60/3125, loss: 19.616, reward: 12.973, critic_reward: 13.398, revenue_rate: 0.3742, distance: 8.9050, memory: 0.2117, power: 0.2696, lr: 0.000400, took: 64.300s
[COOPERATIVE] Epoch 2, Batch 70/3125, loss: -87.905, reward: 13.197, critic_reward: 12.622, revenue_rate: 0.3785, distance: 8.8927, memory: 0.2135, power: 0.2668, lr: 0.000400, took: 63.816s
[COOPERATIVE] Epoch 2, Batch 80/3125, loss: -124.832, reward: 13.689, critic_reward: 12.737, revenue_rate: 0.3948, distance: 9.2766, memory: 0.2300, power: 0.2788, lr: 0.000400, took: 69.935s
[COOPERATIVE] Epoch 2, Batch 90/3125, loss: -37.192, reward: 14.358, critic_reward: 13.967, revenue_rate: 0.4117, distance: 9.4769, memory: 0.2363, power: 0.2887, lr: 0.000400, took: 80.585s
[COOPERATIVE] Epoch 2, Batch 100/3125, loss: -10.565, reward: 15.683, critic_reward: 15.743, revenue_rate: 0.4457, distance: 10.0920, memory: 0.2464, power: 0.3040, lr: 0.000400, took: 84.769s
[COOPERATIVE] Epoch 2, Batch 110/3125, loss: 20.254, reward: 14.784, critic_reward: 15.283, revenue_rate: 0.4220, distance: 9.6540, memory: 0.2362, power: 0.2923, lr: 0.000400, took: 78.166s
[COOPERATIVE] Epoch 2, Batch 120/3125, loss: 29.616, reward: 13.860, critic_reward: 14.458, revenue_rate: 0.3962, distance: 9.1821, memory: 0.2240, power: 0.2773, lr: 0.000400, took: 69.671s
[COOPERATIVE] Epoch 2, Batch 130/3125, loss: -86.736, reward: 14.703, critic_reward: 14.060, revenue_rate: 0.4182, distance: 9.6821, memory: 0.2181, power: 0.2924, lr: 0.000400, took: 80.078s
[COOPERATIVE] Epoch 2, Batch 140/3125, loss: -36.123, reward: 14.530, critic_reward: 14.345, revenue_rate: 0.4182, distance: 9.8019, memory: 0.2375, power: 0.2950, lr: 0.000400, took: 81.269s
[COOPERATIVE] Epoch 2, Batch 150/3125, loss: 14.005, reward: 14.403, critic_reward: 14.716, revenue_rate: 0.4154, distance: 9.8131, memory: 0.2560, power: 0.2947, lr: 0.000400, took: 82.444s
[COOPERATIVE] Epoch 2, Batch 160/3125, loss: -14.483, reward: 14.481, critic_reward: 14.548, revenue_rate: 0.4171, distance: 9.8070, memory: 0.2395, power: 0.2960, lr: 0.000400, took: 78.577s
[COOPERATIVE] Epoch 2, Batch 170/3125, loss: -23.545, reward: 14.625, critic_reward: 14.553, revenue_rate: 0.4214, distance: 9.8683, memory: 0.2415, power: 0.2988, lr: 0.000400, took: 83.077s
[COOPERATIVE] Epoch 2, Batch 180/3125, loss: -25.979, reward: 14.612, critic_reward: 14.646, revenue_rate: 0.4208, distance: 9.8627, memory: 0.2509, power: 0.2963, lr: 0.000400, took: 75.850s
[COOPERATIVE] Epoch 2, Batch 190/3125, loss: 38.326, reward: 13.566, critic_reward: 14.056, revenue_rate: 0.3918, distance: 9.3005, memory: 0.2329, power: 0.2790, lr: 0.000400, took: 66.534s
[COOPERATIVE] Epoch 2, Batch 200/3125, loss: -82.364, reward: 13.941, critic_reward: 13.365, revenue_rate: 0.4018, distance: 9.5344, memory: 0.2230, power: 0.2870, lr: 0.000400, took: 74.598s
[COOPERATIVE] Epoch 2, Batch 210/3125, loss: -26.959, reward: 13.055, critic_reward: 13.022, revenue_rate: 0.3794, distance: 9.2151, memory: 0.2260, power: 0.2772, lr: 0.000400, took: 67.136s
[COOPERATIVE] Epoch 2, Batch 220/3125, loss: -92.293, reward: 13.833, critic_reward: 13.073, revenue_rate: 0.4000, distance: 9.5349, memory: 0.2358, power: 0.2882, lr: 0.000400, took: 70.918s
[COOPERATIVE] Epoch 2, Batch 230/3125, loss: -69.643, reward: 14.500, critic_reward: 13.984, revenue_rate: 0.4156, distance: 9.8422, memory: 0.2401, power: 0.2910, lr: 0.000400, took: 80.395s
[COOPERATIVE] Epoch 2, Batch 240/3125, loss: 34.055, reward: 14.044, critic_reward: 14.737, revenue_rate: 0.4063, distance: 9.6405, memory: 0.2432, power: 0.2921, lr: 0.000400, took: 76.278s
[COOPERATIVE] Epoch 2, Batch 250/3125, loss: 25.251, reward: 14.031, critic_reward: 14.489, revenue_rate: 0.4064, distance: 9.7105, memory: 0.2524, power: 0.2933, lr: 0.000400, took: 77.443s
[COOPERATIVE] Epoch 2, Batch 260/3125, loss: -46.557, reward: 13.901, critic_reward: 13.529, revenue_rate: 0.4035, distance: 9.5948, memory: 0.2360, power: 0.2894, lr: 0.000400, took: 77.017s
[COOPERATIVE] Epoch 2, Batch 270/3125, loss: -130.519, reward: 14.465, critic_reward: 13.317, revenue_rate: 0.4150, distance: 9.7370, memory: 0.2381, power: 0.2942, lr: 0.000400, took: 78.717s
[COOPERATIVE] Epoch 2, Batch 280/3125, loss: 21.658, reward: 13.364, critic_reward: 13.822, revenue_rate: 0.3868, distance: 9.1668, memory: 0.2220, power: 0.2778, lr: 0.000400, took: 65.920s
[COOPERATIVE] Epoch 2, Batch 290/3125, loss: 28.958, reward: 12.984, critic_reward: 13.541, revenue_rate: 0.3741, distance: 8.9105, memory: 0.2120, power: 0.2705, lr: 0.000400, took: 65.436s
[COOPERATIVE] Epoch 2, Batch 300/3125, loss: -37.395, reward: 13.289, critic_reward: 13.070, revenue_rate: 0.3848, distance: 9.1074, memory: 0.2149, power: 0.2746, lr: 0.000400, took: 65.933s
[COOPERATIVE] Epoch 2, Batch 310/3125, loss: 19.554, reward: 12.755, critic_reward: 13.148, revenue_rate: 0.3733, distance: 9.0468, memory: 0.2252, power: 0.2746, lr: 0.000400, took: 66.527s
[COOPERATIVE] Epoch 2, Batch 320/3125, loss: 36.350, reward: 12.402, critic_reward: 12.877, revenue_rate: 0.3610, distance: 8.6662, memory: 0.2130, power: 0.2625, lr: 0.000400, took: 68.400s
[COOPERATIVE] Epoch 2, Batch 330/3125, loss: -49.443, reward: 12.231, critic_reward: 12.019, revenue_rate: 0.3589, distance: 8.8029, memory: 0.2058, power: 0.2646, lr: 0.000400, took: 71.472s
[COOPERATIVE] Epoch 2, Batch 340/3125, loss: -49.340, reward: 11.866, critic_reward: 11.552, revenue_rate: 0.3470, distance: 8.4976, memory: 0.2007, power: 0.2531, lr: 0.000400, took: 72.613s
[COOPERATIVE] Epoch 2, Batch 350/3125, loss: -96.599, reward: 12.560, critic_reward: 11.882, revenue_rate: 0.3655, distance: 8.7991, memory: 0.2098, power: 0.2667, lr: 0.000400, took: 71.197s
[COOPERATIVE] Epoch 2, Batch 360/3125, loss: -67.335, reward: 13.008, critic_reward: 12.460, revenue_rate: 0.3815, distance: 9.3204, memory: 0.2339, power: 0.2827, lr: 0.000400, took: 70.259s
[COOPERATIVE] Epoch 2, Batch 370/3125, loss: -193.834, reward: 15.434, critic_reward: 13.662, revenue_rate: 0.4414, distance: 10.3215, memory: 0.2599, power: 0.3086, lr: 0.000400, took: 86.614s
[COOPERATIVE] Epoch 2, Batch 380/3125, loss: -115.012, reward: 16.292, critic_reward: 15.383, revenue_rate: 0.4666, distance: 10.5934, memory: 0.2595, power: 0.3166, lr: 0.000400, took: 89.417s
[COOPERATIVE] Epoch 2, Batch 390/3125, loss: 0.581, reward: 16.466, critic_reward: 16.562, revenue_rate: 0.4700, distance: 10.6518, memory: 0.2742, power: 0.3227, lr: 0.000400, took: 90.863s
[COOPERATIVE] Epoch 2, Batch 400/3125, loss: 94.962, reward: 15.589, critic_reward: 16.684, revenue_rate: 0.4470, distance: 10.2566, memory: 0.2630, power: 0.3095, lr: 0.000400, took: 92.658s
[COOPERATIVE] Epoch 2, Batch 410/3125, loss: -65.223, reward: 16.444, critic_reward: 16.096, revenue_rate: 0.4657, distance: 10.5259, memory: 0.2632, power: 0.3191, lr: 0.000400, took: 88.363s
[COOPERATIVE] Epoch 2, Batch 420/3125, loss: 38.473, reward: 15.368, critic_reward: 15.917, revenue_rate: 0.4396, distance: 10.1179, memory: 0.2616, power: 0.3054, lr: 0.000400, took: 79.312s
[COOPERATIVE] Epoch 2, Batch 430/3125, loss: -12.887, reward: 15.575, critic_reward: 15.524, revenue_rate: 0.4432, distance: 10.2321, memory: 0.2558, power: 0.3060, lr: 0.000400, took: 82.005s
[COOPERATIVE] Epoch 2, Batch 440/3125, loss: -39.644, reward: 15.637, critic_reward: 15.387, revenue_rate: 0.4508, distance: 10.3745, memory: 0.2568, power: 0.3145, lr: 0.000400, took: 89.535s
[COOPERATIVE] Epoch 2, Batch 450/3125, loss: 13.409, reward: 15.299, critic_reward: 15.655, revenue_rate: 0.4375, distance: 10.1765, memory: 0.2451, power: 0.3071, lr: 0.000400, took: 81.882s
[COOPERATIVE] Epoch 2, Batch 460/3125, loss: -30.175, reward: 15.651, critic_reward: 15.518, revenue_rate: 0.4468, distance: 10.3105, memory: 0.2504, power: 0.3120, lr: 0.000400, took: 85.469s
[COOPERATIVE] Epoch 2, Batch 470/3125, loss: 12.878, reward: 14.903, critic_reward: 15.304, revenue_rate: 0.4298, distance: 10.0094, memory: 0.2477, power: 0.3043, lr: 0.000400, took: 81.138s
[COOPERATIVE] Epoch 2, Batch 480/3125, loss: 8.800, reward: 14.802, critic_reward: 15.058, revenue_rate: 0.4260, distance: 10.0510, memory: 0.2464, power: 0.3026, lr: 0.000400, took: 76.813s
[COOPERATIVE] Epoch 2, Batch 490/3125, loss: 47.439, reward: 14.234, critic_reward: 14.904, revenue_rate: 0.4122, distance: 9.8286, memory: 0.2410, power: 0.2974, lr: 0.000400, took: 77.082s
[COOPERATIVE] Epoch 2, Batch 500/3125, loss: 9.069, reward: 13.870, critic_reward: 14.107, revenue_rate: 0.4028, distance: 9.6336, memory: 0.2481, power: 0.2923, lr: 0.000400, took: 72.902s
[COOPERATIVE] Epoch 2, Batch 510/3125, loss: -126.866, reward: 14.813, critic_reward: 13.781, revenue_rate: 0.4234, distance: 9.7432, memory: 0.2382, power: 0.2978, lr: 0.000400, took: 75.000s
[COOPERATIVE] Epoch 2, Batch 520/3125, loss: -1.566, reward: 14.124, critic_reward: 14.339, revenue_rate: 0.4092, distance: 9.7591, memory: 0.2417, power: 0.2935, lr: 0.000400, took: 74.198s
[COOPERATIVE] Epoch 2, Batch 530/3125, loss: -38.354, reward: 14.239, critic_reward: 14.186, revenue_rate: 0.4112, distance: 9.6677, memory: 0.2507, power: 0.2927, lr: 0.000400, took: 75.317s
[COOPERATIVE] Epoch 2, Batch 540/3125, loss: -13.160, reward: 14.219, critic_reward: 14.209, revenue_rate: 0.4077, distance: 9.5895, memory: 0.2207, power: 0.2891, lr: 0.000400, took: 73.485s
[COOPERATIVE] Epoch 2, Batch 550/3125, loss: -6.651, reward: 14.004, critic_reward: 14.293, revenue_rate: 0.4047, distance: 9.6616, memory: 0.2239, power: 0.2882, lr: 0.000400, took: 74.775s
[COOPERATIVE] Epoch 2, Batch 560/3125, loss: -27.084, reward: 14.083, critic_reward: 13.972, revenue_rate: 0.4050, distance: 9.6788, memory: 0.2407, power: 0.2947, lr: 0.000400, took: 75.039s
[COOPERATIVE] Epoch 2, Batch 570/3125, loss: -139.872, reward: 15.198, critic_reward: 14.017, revenue_rate: 0.4316, distance: 9.8730, memory: 0.2146, power: 0.2972, lr: 0.000400, took: 75.254s
[COOPERATIVE] Epoch 2, Batch 580/3125, loss: -71.172, reward: 15.439, critic_reward: 14.999, revenue_rate: 0.4344, distance: 9.7111, memory: 0.2153, power: 0.2903, lr: 0.000400, took: 73.700s
[COOPERATIVE] Epoch 2, Batch 590/3125, loss: 6.280, reward: 15.349, critic_reward: 15.566, revenue_rate: 0.4392, distance: 10.0196, memory: 0.2243, power: 0.3018, lr: 0.000400, took: 77.903s
[COOPERATIVE] Epoch 2, Batch 600/3125, loss: -88.952, reward: 16.036, critic_reward: 15.327, revenue_rate: 0.4563, distance: 10.5316, memory: 0.2616, power: 0.3187, lr: 0.000400, took: 90.196s
[COOPERATIVE] Epoch 2, Batch 610/3125, loss: -110.158, reward: 16.744, critic_reward: 15.871, revenue_rate: 0.4753, distance: 10.7528, memory: 0.2685, power: 0.3271, lr: 0.000400, took: 101.019s
[COOPERATIVE] Epoch 2, Batch 620/3125, loss: -31.534, reward: 16.767, critic_reward: 16.576, revenue_rate: 0.4738, distance: 10.6553, memory: 0.2601, power: 0.3238, lr: 0.000400, took: 95.237s
[COOPERATIVE] Epoch 2, Batch 630/3125, loss: 19.107, reward: 16.158, critic_reward: 16.566, revenue_rate: 0.4604, distance: 10.5114, memory: 0.2644, power: 0.3154, lr: 0.000400, took: 86.843s
[COOPERATIVE] Epoch 2, Batch 640/3125, loss: -18.088, reward: 16.153, critic_reward: 16.248, revenue_rate: 0.4584, distance: 10.3835, memory: 0.2468, power: 0.3124, lr: 0.000400, took: 86.442s
[COOPERATIVE] Epoch 2, Batch 650/3125, loss: -78.691, reward: 16.781, critic_reward: 16.160, revenue_rate: 0.4746, distance: 10.6316, memory: 0.2511, power: 0.3189, lr: 0.000400, took: 92.756s
[COOPERATIVE] Epoch 2, Batch 660/3125, loss: 73.540, reward: 15.443, critic_reward: 16.298, revenue_rate: 0.4413, distance: 10.2038, memory: 0.2613, power: 0.3047, lr: 0.000400, took: 83.255s
[COOPERATIVE] Epoch 2, Batch 670/3125, loss: 62.548, reward: 14.935, critic_reward: 15.759, revenue_rate: 0.4269, distance: 9.9705, memory: 0.2545, power: 0.2998, lr: 0.000400, took: 75.835s
[COOPERATIVE] Epoch 2, Batch 680/3125, loss: 48.917, reward: 13.696, critic_reward: 14.520, revenue_rate: 0.3919, distance: 9.3159, memory: 0.2180, power: 0.2792, lr: 0.000400, took: 71.771s
[COOPERATIVE] Epoch 2, Batch 690/3125, loss: -35.011, reward: 13.454, critic_reward: 13.309, revenue_rate: 0.3940, distance: 9.5223, memory: 0.2291, power: 0.2892, lr: 0.000400, took: 72.156s
[COOPERATIVE] Epoch 2, Batch 700/3125, loss: -73.558, reward: 13.915, critic_reward: 13.329, revenue_rate: 0.4034, distance: 9.7024, memory: 0.2425, power: 0.2918, lr: 0.000400, took: 74.134s
[COOPERATIVE] Epoch 2, Batch 710/3125, loss: 48.974, reward: 13.090, critic_reward: 13.871, revenue_rate: 0.3810, distance: 9.2470, memory: 0.2291, power: 0.2813, lr: 0.000400, took: 71.261s
[COOPERATIVE] Epoch 2, Batch 720/3125, loss: -26.636, reward: 13.223, critic_reward: 13.396, revenue_rate: 0.3812, distance: 8.9396, memory: 0.1941, power: 0.2692, lr: 0.000400, took: 75.751s
[COOPERATIVE] Epoch 2, Batch 730/3125, loss: -43.196, reward: 12.984, critic_reward: 12.768, revenue_rate: 0.3800, distance: 9.4128, memory: 0.2194, power: 0.2809, lr: 0.000400, took: 72.206s
[COOPERATIVE] Epoch 2, Batch 740/3125, loss: 44.609, reward: 11.876, critic_reward: 12.537, revenue_rate: 0.3494, distance: 8.7565, memory: 0.2080, power: 0.2645, lr: 0.000400, took: 72.791s
[COOPERATIVE] Epoch 2, Batch 750/3125, loss: -13.615, reward: 11.437, critic_reward: 11.894, revenue_rate: 0.3326, distance: 8.0863, memory: 0.1906, power: 0.2444, lr: 0.000400, took: 74.233s
[COOPERATIVE] Epoch 2, Batch 760/3125, loss: 9.359, reward: 10.477, critic_reward: 10.783, revenue_rate: 0.3087, distance: 7.6970, memory: 0.1821, power: 0.2310, lr: 0.000400, took: 71.302s
[COOPERATIVE] Epoch 2, Batch 770/3125, loss: -143.335, reward: 11.639, critic_reward: 10.260, revenue_rate: 0.3366, distance: 8.0946, memory: 0.1825, power: 0.2460, lr: 0.000400, took: 78.501s
[COOPERATIVE] Epoch 2, Batch 780/3125, loss: -62.929, reward: 11.742, critic_reward: 11.169, revenue_rate: 0.3424, distance: 8.3409, memory: 0.2068, power: 0.2504, lr: 0.000400, took: 77.982s
[COOPERATIVE] Epoch 2, Batch 790/3125, loss: 28.727, reward: 11.685, critic_reward: 12.161, revenue_rate: 0.3401, distance: 8.1960, memory: 0.1933, power: 0.2466, lr: 0.000400, took: 77.084s
[COOPERATIVE] Epoch 2, Batch 800/3125, loss: -116.032, reward: 13.596, critic_reward: 12.569, revenue_rate: 0.3913, distance: 9.0995, memory: 0.2155, power: 0.2802, lr: 0.000400, took: 71.093s
[COOPERATIVE] Epoch 2, Batch 810/3125, loss: -84.827, reward: 14.247, critic_reward: 13.538, revenue_rate: 0.4123, distance: 9.7724, memory: 0.2533, power: 0.2960, lr: 0.000400, took: 73.755s
[COOPERATIVE] Epoch 2, Batch 820/3125, loss: -47.765, reward: 15.018, critic_reward: 14.679, revenue_rate: 0.4332, distance: 10.1078, memory: 0.2627, power: 0.3064, lr: 0.000400, took: 79.802s
[COOPERATIVE] Epoch 2, Batch 830/3125, loss: -19.298, reward: 15.422, critic_reward: 15.324, revenue_rate: 0.4431, distance: 10.2468, memory: 0.2585, power: 0.3099, lr: 0.000400, took: 82.730s
[COOPERATIVE] Epoch 2, Batch 840/3125, loss: -52.128, reward: 15.949, critic_reward: 15.544, revenue_rate: 0.4566, distance: 10.4739, memory: 0.2609, power: 0.3168, lr: 0.000400, took: 91.908s
[COOPERATIVE] Epoch 2, Batch 850/3125, loss: -41.431, reward: 16.228, critic_reward: 15.894, revenue_rate: 0.4638, distance: 10.6805, memory: 0.2760, power: 0.3241, lr: 0.000400, took: 96.011s
[COOPERATIVE] Epoch 2, Batch 860/3125, loss: -26.566, reward: 16.432, critic_reward: 16.461, revenue_rate: 0.4689, distance: 10.5719, memory: 0.2584, power: 0.3216, lr: 0.000400, took: 94.164s
[COOPERATIVE] Epoch 2, Batch 870/3125, loss: 94.155, reward: 15.245, critic_reward: 16.423, revenue_rate: 0.4393, distance: 10.2039, memory: 0.2536, power: 0.3100, lr: 0.000400, took: 84.962s
[COOPERATIVE] Epoch 2, Batch 880/3125, loss: 16.021, reward: 15.197, critic_reward: 15.574, revenue_rate: 0.4394, distance: 10.2661, memory: 0.2304, power: 0.3071, lr: 0.000400, took: 84.910s
[COOPERATIVE] Epoch 2, Batch 890/3125, loss: 55.105, reward: 13.983, critic_reward: 14.648, revenue_rate: 0.4093, distance: 9.8339, memory: 0.2413, power: 0.2942, lr: 0.000400, took: 73.354s
[COOPERATIVE] Epoch 2, Batch 900/3125, loss: -108.926, reward: 14.401, critic_reward: 13.661, revenue_rate: 0.4164, distance: 9.8160, memory: 0.2403, power: 0.2951, lr: 0.000400, took: 74.965s
[COOPERATIVE] Epoch 2, Batch 910/3125, loss: -164.749, reward: 15.121, critic_reward: 13.795, revenue_rate: 0.4366, distance: 10.3116, memory: 0.2444, power: 0.3046, lr: 0.000400, took: 79.237s
[COOPERATIVE] Epoch 2, Batch 920/3125, loss: -53.135, reward: 15.076, critic_reward: 14.716, revenue_rate: 0.4340, distance: 10.1561, memory: 0.2575, power: 0.3056, lr: 0.000400, took: 81.148s
[COOPERATIVE] Epoch 2, Batch 930/3125, loss: 1.385, reward: 14.987, critic_reward: 15.138, revenue_rate: 0.4310, distance: 10.0562, memory: 0.2510, power: 0.3045, lr: 0.000400, took: 78.319s
[COOPERATIVE] Epoch 2, Batch 940/3125, loss: -77.711, reward: 15.802, critic_reward: 15.260, revenue_rate: 0.4490, distance: 10.2986, memory: 0.2599, power: 0.3115, lr: 0.000400, took: 86.286s
[COOPERATIVE] Epoch 2, Batch 950/3125, loss: -26.557, reward: 15.517, critic_reward: 15.414, revenue_rate: 0.4401, distance: 10.0287, memory: 0.2311, power: 0.3026, lr: 0.000400, took: 78.048s
[COOPERATIVE] Epoch 2, Batch 960/3125, loss: 16.661, reward: 15.213, critic_reward: 15.536, revenue_rate: 0.4303, distance: 9.7319, memory: 0.2323, power: 0.2989, lr: 0.000400, took: 74.432s
[COOPERATIVE] Epoch 2, Batch 970/3125, loss: -47.974, reward: 15.777, critic_reward: 15.510, revenue_rate: 0.4477, distance: 10.1140, memory: 0.2390, power: 0.3037, lr: 0.000400, took: 81.267s
[COOPERATIVE] Epoch 2, Batch 980/3125, loss: -83.423, reward: 16.369, critic_reward: 15.862, revenue_rate: 0.4584, distance: 10.1079, memory: 0.2440, power: 0.3056, lr: 0.000400, took: 78.366s
[COOPERATIVE] Epoch 2, Batch 990/3125, loss: 40.263, reward: 15.820, critic_reward: 16.394, revenue_rate: 0.4508, distance: 10.1371, memory: 0.2512, power: 0.3043, lr: 0.000400, took: 80.184s
[COOPERATIVE] Epoch 2, Batch 1000/3125, loss: 28.057, reward: 15.472, critic_reward: 16.012, revenue_rate: 0.4429, distance: 10.2321, memory: 0.2357, power: 0.3075, lr: 0.000400, took: 83.832s
[COOPERATIVE] Epoch 2, Batch 1010/3125, loss: 11.719, reward: 14.860, critic_reward: 15.343, revenue_rate: 0.4251, distance: 9.8290, memory: 0.2508, power: 0.2959, lr: 0.000400, took: 76.873s
[COOPERATIVE] Epoch 2, Batch 1020/3125, loss: 3.844, reward: 14.581, critic_reward: 14.790, revenue_rate: 0.4202, distance: 9.7131, memory: 0.2331, power: 0.2916, lr: 0.000400, took: 75.943s
[COOPERATIVE] Epoch 2, Batch 1030/3125, loss: -62.208, reward: 15.210, critic_reward: 14.735, revenue_rate: 0.4363, distance: 10.0517, memory: 0.2521, power: 0.3030, lr: 0.000400, took: 79.561s
[COOPERATIVE] Epoch 2, Batch 1040/3125, loss: 46.222, reward: 14.267, critic_reward: 14.920, revenue_rate: 0.4135, distance: 9.7902, memory: 0.2398, power: 0.2966, lr: 0.000400, took: 74.877s
[COOPERATIVE] Epoch 2, Batch 1050/3125, loss: 34.680, reward: 13.729, critic_reward: 14.280, revenue_rate: 0.3986, distance: 9.5653, memory: 0.2152, power: 0.2899, lr: 0.000400, took: 73.153s
[COOPERATIVE] Epoch 2, Batch 1060/3125, loss: -2.554, reward: 13.369, critic_reward: 13.510, revenue_rate: 0.3930, distance: 9.5704, memory: 0.2178, power: 0.2881, lr: 0.000400, took: 74.162s
[COOPERATIVE] Epoch 2, Batch 1070/3125, loss: -44.312, reward: 13.700, critic_reward: 13.444, revenue_rate: 0.3948, distance: 9.3382, memory: 0.2301, power: 0.2859, lr: 0.000400, took: 72.851s
[COOPERATIVE] Epoch 2, Batch 1080/3125, loss: -35.968, reward: 13.652, critic_reward: 13.707, revenue_rate: 0.3980, distance: 9.5232, memory: 0.2238, power: 0.2862, lr: 0.000400, took: 75.739s
[COOPERATIVE] Epoch 2, Batch 1090/3125, loss: 25.449, reward: 13.105, critic_reward: 13.525, revenue_rate: 0.3806, distance: 9.1216, memory: 0.2163, power: 0.2802, lr: 0.000400, took: 71.310s
[COOPERATIVE] Epoch 2, Batch 1100/3125, loss: 11.010, reward: 12.829, critic_reward: 13.178, revenue_rate: 0.3699, distance: 8.6531, memory: 0.1970, power: 0.2626, lr: 0.000400, took: 71.788s
[COOPERATIVE] Epoch 2, Batch 1110/3125, loss: 24.611, reward: 12.192, critic_reward: 12.634, revenue_rate: 0.3407, distance: 7.4723, memory: 0.1516, power: 0.2278, lr: 0.000400, took: 67.818s
[COOPERATIVE] Epoch 2, Batch 1120/3125, loss: -147.537, reward: 12.995, critic_reward: 12.048, revenue_rate: 0.3751, distance: 8.9378, memory: 0.2136, power: 0.2673, lr: 0.000400, took: 76.026s
[COOPERATIVE] Epoch 2, Batch 1130/3125, loss: -329.951, reward: 14.591, critic_reward: 12.325, revenue_rate: 0.4151, distance: 9.3926, memory: 0.2301, power: 0.2872, lr: 0.000400, took: 73.722s
[COOPERATIVE] Epoch 2, Batch 1140/3125, loss: -135.042, reward: 14.773, critic_reward: 13.637, revenue_rate: 0.4234, distance: 9.7994, memory: 0.2397, power: 0.2937, lr: 0.000400, took: 76.350s
[COOPERATIVE] Epoch 2, Batch 1150/3125, loss: -5.647, reward: 14.957, critic_reward: 15.041, revenue_rate: 0.4281, distance: 10.0794, memory: 0.2465, power: 0.3009, lr: 0.000400, took: 79.603s
[COOPERATIVE] Epoch 2, Batch 1160/3125, loss: 58.319, reward: 14.872, critic_reward: 15.623, revenue_rate: 0.4244, distance: 9.8166, memory: 0.2303, power: 0.2979, lr: 0.000400, took: 77.920s
[COOPERATIVE] Epoch 2, Batch 1170/3125, loss: -41.251, reward: 15.441, critic_reward: 15.245, revenue_rate: 0.4404, distance: 10.0261, memory: 0.2616, power: 0.3107, lr: 0.000400, took: 82.291s
[COOPERATIVE] Epoch 2, Batch 1180/3125, loss: 15.489, reward: 14.833, critic_reward: 15.146, revenue_rate: 0.4294, distance: 10.1377, memory: 0.2558, power: 0.3044, lr: 0.000400, took: 80.450s
[COOPERATIVE] Epoch 2, Batch 1190/3125, loss: -56.011, reward: 15.256, critic_reward: 14.840, revenue_rate: 0.4385, distance: 10.2828, memory: 0.2578, power: 0.3087, lr: 0.000400, took: 84.581s
[COOPERATIVE] Epoch 2, Batch 1200/3125, loss: -50.602, reward: 15.129, critic_reward: 14.792, revenue_rate: 0.4345, distance: 10.0904, memory: 0.2361, power: 0.3051, lr: 0.000400, took: 80.509s
[COOPERATIVE] Epoch 2, Batch 1210/3125, loss: 10.525, reward: 14.582, critic_reward: 14.874, revenue_rate: 0.4216, distance: 9.9882, memory: 0.2595, power: 0.3016, lr: 0.000400, took: 77.404s
[COOPERATIVE] Epoch 2, Batch 1220/3125, loss: -14.609, reward: 14.936, critic_reward: 14.928, revenue_rate: 0.4306, distance: 10.1499, memory: 0.2566, power: 0.3079, lr: 0.000400, took: 81.597s
[COOPERATIVE] Epoch 2, Batch 1230/3125, loss: -57.917, reward: 15.651, critic_reward: 15.273, revenue_rate: 0.4447, distance: 10.1178, memory: 0.2534, power: 0.3070, lr: 0.000400, took: 78.819s
[COOPERATIVE] Epoch 2, Batch 1240/3125, loss: -2.476, reward: 15.606, critic_reward: 15.694, revenue_rate: 0.4461, distance: 10.3712, memory: 0.2590, power: 0.3138, lr: 0.000400, took: 84.426s
[COOPERATIVE] Epoch 2, Batch 1250/3125, loss: 40.020, reward: 15.233, critic_reward: 15.866, revenue_rate: 0.4353, distance: 10.0813, memory: 0.2528, power: 0.3095, lr: 0.000400, took: 82.403s
[COOPERATIVE] Epoch 2, Batch 1260/3125, loss: 69.599, reward: 14.240, critic_reward: 15.273, revenue_rate: 0.4130, distance: 9.9166, memory: 0.2408, power: 0.2962, lr: 0.000400, took: 77.912s
[COOPERATIVE] Epoch 2, Batch 1270/3125, loss: -68.874, reward: 14.561, critic_reward: 14.104, revenue_rate: 0.4216, distance: 9.9128, memory: 0.2376, power: 0.2957, lr: 0.000400, took: 76.576s
[COOPERATIVE] Epoch 2, Batch 1280/3125, loss: -82.467, reward: 14.551, critic_reward: 13.825, revenue_rate: 0.4197, distance: 9.9453, memory: 0.2347, power: 0.2982, lr: 0.000400, took: 75.358s
[COOPERATIVE] Epoch 2, Batch 1290/3125, loss: -82.073, reward: 15.325, critic_reward: 14.583, revenue_rate: 0.4404, distance: 10.1222, memory: 0.2496, power: 0.3092, lr: 0.000400, took: 81.524s
[COOPERATIVE] Epoch 2, Batch 1300/3125, loss: -12.389, reward: 15.581, critic_reward: 15.594, revenue_rate: 0.4444, distance: 10.0682, memory: 0.2517, power: 0.3051, lr: 0.000400, took: 80.036s
[COOPERATIVE] Epoch 2, Batch 1310/3125, loss: -22.409, reward: 15.910, critic_reward: 15.799, revenue_rate: 0.4569, distance: 10.3988, memory: 0.2584, power: 0.3190, lr: 0.000400, took: 90.467s
[COOPERATIVE] Epoch 2, Batch 1320/3125, loss: -18.781, reward: 15.716, critic_reward: 15.826, revenue_rate: 0.4515, distance: 10.5778, memory: 0.2547, power: 0.3176, lr: 0.000400, took: 87.568s
[COOPERATIVE] Epoch 2, Batch 1330/3125, loss: 8.439, reward: 15.504, critic_reward: 15.750, revenue_rate: 0.4411, distance: 10.0597, memory: 0.2285, power: 0.3034, lr: 0.000400, took: 79.861s
[COOPERATIVE] Epoch 2, Batch 1340/3125, loss: 71.841, reward: 14.633, critic_reward: 15.597, revenue_rate: 0.4188, distance: 9.7222, memory: 0.2202, power: 0.2943, lr: 0.000400, took: 74.418s
[COOPERATIVE] Epoch 2, Batch 1350/3125, loss: -55.686, reward: 15.274, critic_reward: 14.847, revenue_rate: 0.4364, distance: 9.9755, memory: 0.2432, power: 0.3060, lr: 0.000400, took: 76.988s
[COOPERATIVE] Epoch 2, Batch 1360/3125, loss: -86.445, reward: 15.791, critic_reward: 15.110, revenue_rate: 0.4495, distance: 10.2717, memory: 0.2475, power: 0.3078, lr: 0.000400, took: 84.245s
[COOPERATIVE] Epoch 2, Batch 1370/3125, loss: 23.289, reward: 15.410, critic_reward: 15.739, revenue_rate: 0.4402, distance: 10.1001, memory: 0.2498, power: 0.3047, lr: 0.000400, took: 78.846s
[COOPERATIVE] Epoch 2, Batch 1380/3125, loss: -22.900, reward: 15.647, critic_reward: 15.635, revenue_rate: 0.4469, distance: 10.1416, memory: 0.2426, power: 0.3071, lr: 0.000400, took: 79.074s
[COOPERATIVE] Epoch 2, Batch 1390/3125, loss: -56.785, reward: 15.463, critic_reward: 15.332, revenue_rate: 0.4422, distance: 10.3277, memory: 0.2399, power: 0.3080, lr: 0.000400, took: 81.260s
[COOPERATIVE] Epoch 2, Batch 1400/3125, loss: -88.716, reward: 15.590, critic_reward: 15.141, revenue_rate: 0.4433, distance: 10.1309, memory: 0.2374, power: 0.3052, lr: 0.000400, took: 82.043s
[COOPERATIVE] Epoch 2, Batch 1410/3125, loss: -89.640, reward: 16.043, critic_reward: 15.523, revenue_rate: 0.4577, distance: 10.4388, memory: 0.2612, power: 0.3164, lr: 0.000400, took: 83.171s
[COOPERATIVE] Epoch 2, Batch 1420/3125, loss: 91.473, reward: 14.973, critic_reward: 16.046, revenue_rate: 0.4317, distance: 10.1526, memory: 0.2540, power: 0.3034, lr: 0.000400, took: 78.494s
[COOPERATIVE] Epoch 2, Batch 1430/3125, loss: 20.771, reward: 15.131, critic_reward: 15.521, revenue_rate: 0.4302, distance: 9.9554, memory: 0.2353, power: 0.3014, lr: 0.000400, took: 76.047s
[COOPERATIVE] Epoch 2, Batch 1440/3125, loss: -80.069, reward: 15.465, critic_reward: 15.007, revenue_rate: 0.4407, distance: 10.1332, memory: 0.2342, power: 0.3061, lr: 0.000400, took: 78.641s
[COOPERATIVE] Epoch 2, Batch 1450/3125, loss: 30.803, reward: 14.681, critic_reward: 15.116, revenue_rate: 0.4230, distance: 9.8897, memory: 0.2308, power: 0.2999, lr: 0.000400, took: 73.962s
[COOPERATIVE] Epoch 2, Batch 1460/3125, loss: -26.234, reward: 14.458, critic_reward: 14.363, revenue_rate: 0.4174, distance: 9.8438, memory: 0.2352, power: 0.2992, lr: 0.000400, took: 76.684s
[COOPERATIVE] Epoch 2, Batch 1470/3125, loss: -31.085, reward: 13.939, critic_reward: 13.949, revenue_rate: 0.4035, distance: 9.6583, memory: 0.2306, power: 0.2941, lr: 0.000400, took: 76.182s
[COOPERATIVE] Epoch 2, Batch 1480/3125, loss: -30.410, reward: 13.914, critic_reward: 13.743, revenue_rate: 0.4042, distance: 9.7048, memory: 0.2246, power: 0.2927, lr: 0.000400, took: 73.771s
[COOPERATIVE] Epoch 2, Batch 1490/3125, loss: -55.656, reward: 14.273, critic_reward: 13.969, revenue_rate: 0.4150, distance: 9.8958, memory: 0.2236, power: 0.2964, lr: 0.000400, took: 75.482s
[COOPERATIVE] Epoch 2, Batch 1500/3125, loss: -68.737, reward: 15.069, critic_reward: 14.545, revenue_rate: 0.4389, distance: 10.3065, memory: 0.2414, power: 0.3108, lr: 0.000400, took: 82.478s
[COOPERATIVE] Epoch 2, Batch 1510/3125, loss: -23.149, reward: 15.465, critic_reward: 15.300, revenue_rate: 0.4436, distance: 10.1223, memory: 0.2465, power: 0.3092, lr: 0.000400, took: 80.353s
[COOPERATIVE] Epoch 2, Batch 1520/3125, loss: 51.587, reward: 15.189, critic_reward: 15.865, revenue_rate: 0.4394, distance: 10.3793, memory: 0.2541, power: 0.3099, lr: 0.000400, took: 85.786s
[COOPERATIVE] Epoch 2, Batch 1530/3125, loss: 103.205, reward: 14.213, critic_reward: 15.404, revenue_rate: 0.4112, distance: 9.8539, memory: 0.2364, power: 0.2961, lr: 0.000400, took: 75.847s
[COOPERATIVE] Epoch 2, Batch 1540/3125, loss: -8.451, reward: 14.088, critic_reward: 14.167, revenue_rate: 0.4093, distance: 9.9301, memory: 0.2445, power: 0.2984, lr: 0.000400, took: 77.095s
[COOPERATIVE] Epoch 2, Batch 1550/3125, loss: -89.796, reward: 14.704, critic_reward: 14.010, revenue_rate: 0.4255, distance: 10.1386, memory: 0.2477, power: 0.3056, lr: 0.000400, took: 80.877s
[COOPERATIVE] Epoch 2, Batch 1560/3125, loss: -16.817, reward: 13.968, critic_reward: 14.163, revenue_rate: 0.4031, distance: 9.6227, memory: 0.2378, power: 0.2925, lr: 0.000400, took: 76.092s
[COOPERATIVE] Epoch 2, Batch 1570/3125, loss: -20.661, reward: 13.721, critic_reward: 13.862, revenue_rate: 0.3973, distance: 9.5638, memory: 0.2319, power: 0.2887, lr: 0.000400, took: 72.968s
[COOPERATIVE] Epoch 2, Batch 1580/3125, loss: -18.485, reward: 13.507, critic_reward: 13.454, revenue_rate: 0.3951, distance: 9.5880, memory: 0.2378, power: 0.2901, lr: 0.000400, took: 73.708s
[COOPERATIVE] Epoch 2, Batch 1590/3125, loss: -26.516, reward: 12.960, critic_reward: 12.929, revenue_rate: 0.3762, distance: 9.1544, memory: 0.2265, power: 0.2813, lr: 0.000400, took: 73.051s
[COOPERATIVE] Epoch 2, Batch 1600/3125, loss: -56.948, reward: 13.158, critic_reward: 12.665, revenue_rate: 0.3868, distance: 9.4439, memory: 0.2299, power: 0.2859, lr: 0.000400, took: 73.162s
[COOPERATIVE] Epoch 2, Batch 1610/3125, loss: -161.058, reward: 14.588, critic_reward: 13.082, revenue_rate: 0.4193, distance: 9.9486, memory: 0.2401, power: 0.3002, lr: 0.000400, took: 77.069s
[COOPERATIVE] Epoch 2, Batch 1620/3125, loss: -28.798, reward: 14.337, critic_reward: 14.156, revenue_rate: 0.4127, distance: 9.8502, memory: 0.2428, power: 0.2973, lr: 0.000400, took: 77.170s
[COOPERATIVE] Epoch 2, Batch 1630/3125, loss: -74.660, reward: 15.283, critic_reward: 14.812, revenue_rate: 0.4379, distance: 10.3085, memory: 0.2465, power: 0.3073, lr: 0.000400, took: 85.751s
[COOPERATIVE] Epoch 2, Batch 1640/3125, loss: 43.735, reward: 14.482, critic_reward: 15.155, revenue_rate: 0.4235, distance: 10.1153, memory: 0.2551, power: 0.3045, lr: 0.000400, took: 80.861s
[COOPERATIVE] Epoch 2, Batch 1650/3125, loss: -6.870, reward: 14.681, critic_reward: 14.769, revenue_rate: 0.4232, distance: 9.9816, memory: 0.2487, power: 0.3049, lr: 0.000400, took: 78.402s
[COOPERATIVE] Epoch 2, Batch 1660/3125, loss: 5.163, reward: 14.323, critic_reward: 14.526, revenue_rate: 0.4150, distance: 9.8399, memory: 0.2471, power: 0.2991, lr: 0.000400, took: 76.863s
[COOPERATIVE] Epoch 2, Batch 1670/3125, loss: 35.389, reward: 13.747, critic_reward: 14.281, revenue_rate: 0.4032, distance: 9.7077, memory: 0.2293, power: 0.2943, lr: 0.000400, took: 76.389s
[COOPERATIVE] Epoch 2, Batch 1680/3125, loss: -65.054, reward: 13.931, critic_reward: 13.661, revenue_rate: 0.4019, distance: 9.4693, memory: 0.2406, power: 0.2936, lr: 0.000400, took: 76.719s
[COOPERATIVE] Epoch 2, Batch 1690/3125, loss: -78.194, reward: 14.123, critic_reward: 13.543, revenue_rate: 0.4085, distance: 9.7334, memory: 0.2413, power: 0.2951, lr: 0.000400, took: 77.503s
[COOPERATIVE] Epoch 2, Batch 1700/3125, loss: -34.663, reward: 14.233, critic_reward: 14.055, revenue_rate: 0.4124, distance: 9.8778, memory: 0.2406, power: 0.2988, lr: 0.000400, took: 75.895s
[COOPERATIVE] Epoch 2, Batch 1710/3125, loss: -46.020, reward: 14.520, critic_reward: 14.259, revenue_rate: 0.4192, distance: 9.9429, memory: 0.2467, power: 0.2991, lr: 0.000400, took: 77.187s
[COOPERATIVE] Epoch 2, Batch 1720/3125, loss: -22.229, reward: 14.133, critic_reward: 14.164, revenue_rate: 0.4105, distance: 9.6708, memory: 0.2320, power: 0.2968, lr: 0.000400, took: 76.327s
[COOPERATIVE] Epoch 2, Batch 1730/3125, loss: -23.520, reward: 13.895, critic_reward: 13.940, revenue_rate: 0.4016, distance: 9.5958, memory: 0.2278, power: 0.2875, lr: 0.000400, took: 72.851s
[COOPERATIVE] Epoch 2, Batch 1740/3125, loss: 42.503, reward: 13.337, critic_reward: 13.958, revenue_rate: 0.3903, distance: 9.5157, memory: 0.2304, power: 0.2870, lr: 0.000400, took: 73.088s
[COOPERATIVE] Epoch 2, Batch 1750/3125, loss: 37.073, reward: 12.950, critic_reward: 13.475, revenue_rate: 0.3765, distance: 9.1347, memory: 0.2119, power: 0.2787, lr: 0.000400, took: 72.818s
[COOPERATIVE] Epoch 2, Batch 1760/3125, loss: -27.183, reward: 12.845, critic_reward: 12.718, revenue_rate: 0.3727, distance: 9.1255, memory: 0.2163, power: 0.2776, lr: 0.000400, took: 71.088s
[COOPERATIVE] Epoch 2, Batch 1770/3125, loss: -69.023, reward: 13.204, critic_reward: 12.642, revenue_rate: 0.3849, distance: 9.1683, memory: 0.2174, power: 0.2765, lr: 0.000400, took: 72.358s
[COOPERATIVE] Epoch 2, Batch 1780/3125, loss: -30.358, reward: 12.805, critic_reward: 12.866, revenue_rate: 0.3732, distance: 9.0047, memory: 0.2114, power: 0.2714, lr: 0.000400, took: 74.133s
[COOPERATIVE] Epoch 2, Batch 1790/3125, loss: 4.382, reward: 12.133, critic_reward: 12.421, revenue_rate: 0.3574, distance: 8.8824, memory: 0.2139, power: 0.2662, lr: 0.000400, took: 74.173s
[COOPERATIVE] Epoch 2, Batch 1800/3125, loss: -97.330, reward: 12.973, critic_reward: 12.068, revenue_rate: 0.3788, distance: 9.2303, memory: 0.2220, power: 0.2801, lr: 0.000400, took: 71.448s
[COOPERATIVE] Epoch 2, Batch 1810/3125, loss: 2.814, reward: 12.336, critic_reward: 12.549, revenue_rate: 0.3580, distance: 8.8280, memory: 0.2005, power: 0.2652, lr: 0.000400, took: 73.881s
[COOPERATIVE] Epoch 2, Batch 1820/3125, loss: -45.216, reward: 12.413, critic_reward: 12.154, revenue_rate: 0.3659, distance: 9.0414, memory: 0.2209, power: 0.2725, lr: 0.000400, took: 71.922s
[COOPERATIVE] Epoch 2, Batch 1830/3125, loss: -201.923, reward: 14.179, critic_reward: 12.169, revenue_rate: 0.4120, distance: 9.8495, memory: 0.2335, power: 0.2972, lr: 0.000400, took: 75.643s
[COOPERATIVE] Epoch 2, Batch 1840/3125, loss: -105.860, reward: 14.189, critic_reward: 13.315, revenue_rate: 0.4108, distance: 9.8676, memory: 0.2429, power: 0.2998, lr: 0.000400, took: 76.705s
[COOPERATIVE] Epoch 2, Batch 1850/3125, loss: 34.021, reward: 13.693, critic_reward: 14.284, revenue_rate: 0.4009, distance: 9.8424, memory: 0.2406, power: 0.2946, lr: 0.000400, took: 73.935s
[COOPERATIVE] Epoch 2, Batch 1860/3125, loss: -19.212, reward: 14.486, critic_reward: 14.449, revenue_rate: 0.4176, distance: 9.9514, memory: 0.2304, power: 0.3011, lr: 0.000400, took: 77.133s
[COOPERATIVE] Epoch 2, Batch 1870/3125, loss: 78.000, reward: 13.257, critic_reward: 14.321, revenue_rate: 0.3879, distance: 9.5676, memory: 0.2559, power: 0.2870, lr: 0.000400, took: 74.935s
[COOPERATIVE] Epoch 2, Batch 1880/3125, loss: -83.167, reward: 14.205, critic_reward: 13.505, revenue_rate: 0.4093, distance: 9.7163, memory: 0.2287, power: 0.2944, lr: 0.000400, took: 73.832s
[COOPERATIVE] Epoch 2, Batch 1890/3125, loss: -174.508, reward: 15.291, critic_reward: 13.678, revenue_rate: 0.4362, distance: 10.0892, memory: 0.2592, power: 0.3105, lr: 0.000400, took: 83.596s
[COOPERATIVE] Epoch 2, Batch 1900/3125, loss: 8.019, reward: 14.614, critic_reward: 14.819, revenue_rate: 0.4240, distance: 10.0069, memory: 0.2578, power: 0.2995, lr: 0.000400, took: 74.625s
[COOPERATIVE] Epoch 2, Batch 1910/3125, loss: 108.372, reward: 13.484, critic_reward: 14.859, revenue_rate: 0.3937, distance: 9.4890, memory: 0.2358, power: 0.2897, lr: 0.000400, took: 72.867s
[COOPERATIVE] Epoch 2, Batch 1920/3125, loss: 75.677, reward: 12.488, critic_reward: 13.638, revenue_rate: 0.3649, distance: 8.9529, memory: 0.2121, power: 0.2722, lr: 0.000400, took: 72.147s
[COOPERATIVE] Epoch 2, Batch 1930/3125, loss: 58.513, reward: 11.579, critic_reward: 12.433, revenue_rate: 0.3437, distance: 8.7525, memory: 0.2222, power: 0.2619, lr: 0.000400, took: 76.114s
[COOPERATIVE] Epoch 2, Batch 1940/3125, loss: -102.317, reward: 12.456, critic_reward: 11.545, revenue_rate: 0.3694, distance: 9.1301, memory: 0.2187, power: 0.2719, lr: 0.000400, took: 72.382s
[COOPERATIVE] Epoch 2, Batch 1950/3125, loss: -243.096, reward: 14.134, critic_reward: 11.991, revenue_rate: 0.4079, distance: 9.6759, memory: 0.2400, power: 0.2914, lr: 0.000400, took: 73.751s
[COOPERATIVE] Epoch 2, Batch 1960/3125, loss: -65.201, reward: 13.821, critic_reward: 13.299, revenue_rate: 0.4026, distance: 9.6012, memory: 0.2362, power: 0.2928, lr: 0.000400, took: 73.008s
[COOPERATIVE] Epoch 2, Batch 1970/3125, loss: -52.100, reward: 14.317, critic_reward: 14.012, revenue_rate: 0.4124, distance: 9.7036, memory: 0.2388, power: 0.2963, lr: 0.000400, took: 75.769s
[COOPERATIVE] Epoch 2, Batch 1980/3125, loss: 23.096, reward: 13.763, critic_reward: 14.211, revenue_rate: 0.3970, distance: 9.5008, memory: 0.2259, power: 0.2849, lr: 0.000400, took: 72.478s
[COOPERATIVE] Epoch 2, Batch 1990/3125, loss: 22.177, reward: 13.500, critic_reward: 13.958, revenue_rate: 0.3930, distance: 9.4548, memory: 0.2293, power: 0.2864, lr: 0.000400, took: 72.315s
[COOPERATIVE] Epoch 2, Batch 2000/3125, loss: 64.472, reward: 12.458, critic_reward: 13.350, revenue_rate: 0.3687, distance: 9.1707, memory: 0.2344, power: 0.2767, lr: 0.000400, took: 72.749s
[COOPERATIVE] Epoch 2, Batch 2010/3125, loss: -81.481, reward: 13.153, critic_reward: 12.448, revenue_rate: 0.3840, distance: 9.3125, memory: 0.2207, power: 0.2804, lr: 0.000400, took: 71.686s
[COOPERATIVE] Epoch 2, Batch 2020/3125, loss: -74.868, reward: 13.238, critic_reward: 12.840, revenue_rate: 0.3826, distance: 9.1813, memory: 0.2334, power: 0.2763, lr: 0.000400, took: 74.647s
[COOPERATIVE] Epoch 2, Batch 2030/3125, loss: 130.528, reward: 11.211, critic_reward: 13.042, revenue_rate: 0.3297, distance: 8.2255, memory: 0.2003, power: 0.2459, lr: 0.000400, took: 71.482s
[COOPERATIVE] Epoch 2, Batch 2040/3125, loss: 95.152, reward: 10.464, critic_reward: 11.978, revenue_rate: 0.3027, distance: 7.2037, memory: 0.1634, power: 0.2172, lr: 0.000400, took: 58.621s
[COOPERATIVE] Epoch 2, Batch 2050/3125, loss: 41.131, reward: 9.710, critic_reward: 10.750, revenue_rate: 0.2784, distance: 6.4511, memory: 0.1341, power: 0.1908, lr: 0.000400, took: 50.332s
[COOPERATIVE] Epoch 2, Batch 2060/3125, loss: -32.411, reward: 9.727, critic_reward: 9.615, revenue_rate: 0.2832, distance: 6.8267, memory: 0.1546, power: 0.2076, lr: 0.000400, took: 55.001s
[COOPERATIVE] Epoch 2, Batch 2070/3125, loss: -141.003, reward: 10.986, critic_reward: 9.488, revenue_rate: 0.3243, distance: 8.1709, memory: 0.1950, power: 0.2498, lr: 0.000400, took: 79.604s
[COOPERATIVE] Epoch 2, Batch 2080/3125, loss: -107.714, reward: 11.778, critic_reward: 10.653, revenue_rate: 0.3484, distance: 8.7526, memory: 0.2079, power: 0.2656, lr: 0.000400, took: 74.869s
[COOPERATIVE] Epoch 2, Batch 2090/3125, loss: -60.504, reward: 12.505, critic_reward: 12.033, revenue_rate: 0.3663, distance: 8.9476, memory: 0.2145, power: 0.2711, lr: 0.000400, took: 70.612s
[COOPERATIVE] Epoch 2, Batch 2100/3125, loss: -6.963, reward: 12.805, critic_reward: 12.969, revenue_rate: 0.3744, distance: 9.2368, memory: 0.2067, power: 0.2743, lr: 0.000400, took: 70.769s
[COOPERATIVE] Epoch 2, Batch 2110/3125, loss: -47.127, reward: 13.356, critic_reward: 13.093, revenue_rate: 0.3884, distance: 9.3957, memory: 0.2289, power: 0.2854, lr: 0.000400, took: 72.316s
[COOPERATIVE] Epoch 2, Batch 2120/3125, loss: -6.610, reward: 12.999, critic_reward: 13.113, revenue_rate: 0.3802, distance: 9.3861, memory: 0.2376, power: 0.2846, lr: 0.000400, took: 72.308s
[COOPERATIVE] Epoch 2, Batch 2130/3125, loss: 11.459, reward: 13.100, critic_reward: 13.407, revenue_rate: 0.3833, distance: 9.4192, memory: 0.2229, power: 0.2820, lr: 0.000400, took: 72.811s
[COOPERATIVE] Epoch 2, Batch 2140/3125, loss: 4.957, reward: 12.926, critic_reward: 13.096, revenue_rate: 0.3786, distance: 9.3750, memory: 0.2127, power: 0.2810, lr: 0.000400, took: 71.931s
[COOPERATIVE] Epoch 2, Batch 2150/3125, loss: -86.600, reward: 13.228, critic_reward: 12.546, revenue_rate: 0.3847, distance: 9.3406, memory: 0.2229, power: 0.2833, lr: 0.000400, took: 72.667s
[COOPERATIVE] Epoch 2, Batch 2160/3125, loss: -182.415, reward: 14.508, critic_reward: 13.018, revenue_rate: 0.4205, distance: 9.9813, memory: 0.2365, power: 0.2998, lr: 0.000400, took: 77.238s
[COOPERATIVE] Epoch 2, Batch 2170/3125, loss: -70.168, reward: 14.460, critic_reward: 13.976, revenue_rate: 0.4158, distance: 9.8729, memory: 0.2410, power: 0.2976, lr: 0.000400, took: 76.881s
[COOPERATIVE] Epoch 2, Batch 2180/3125, loss: 20.923, reward: 14.147, critic_reward: 14.472, revenue_rate: 0.4073, distance: 9.7210, memory: 0.2337, power: 0.2925, lr: 0.000400, took: 74.077s
[COOPERATIVE] Epoch 2, Batch 2190/3125, loss: -83.118, reward: 15.029, critic_reward: 14.441, revenue_rate: 0.4295, distance: 9.8925, memory: 0.2336, power: 0.2998, lr: 0.000400, took: 77.523s
[COOPERATIVE] Epoch 2, Batch 2200/3125, loss: 31.285, reward: 14.245, critic_reward: 14.672, revenue_rate: 0.4083, distance: 9.5338, memory: 0.2250, power: 0.2878, lr: 0.000400, took: 72.622s
[COOPERATIVE] Epoch 2, Batch 2210/3125, loss: 25.844, reward: 13.731, critic_reward: 14.148, revenue_rate: 0.3970, distance: 9.4578, memory: 0.2158, power: 0.2858, lr: 0.000400, took: 72.396s
[COOPERATIVE] Epoch 2, Batch 2220/3125, loss: 71.506, reward: 12.607, critic_reward: 13.612, revenue_rate: 0.3693, distance: 9.2011, memory: 0.2415, power: 0.2769, lr: 0.000400, took: 71.721s
[COOPERATIVE] Epoch 2, Batch 2230/3125, loss: -90.788, reward: 13.461, critic_reward: 12.737, revenue_rate: 0.3938, distance: 9.6918, memory: 0.2259, power: 0.2893, lr: 0.000400, took: 74.374s
[COOPERATIVE] Epoch 2, Batch 2240/3125, loss: -97.346, reward: 13.606, critic_reward: 12.986, revenue_rate: 0.3967, distance: 9.6634, memory: 0.2444, power: 0.2900, lr: 0.000400, took: 75.740s
[COOPERATIVE] Epoch 2, Batch 2250/3125, loss: -61.340, reward: 13.672, critic_reward: 13.211, revenue_rate: 0.3978, distance: 9.6984, memory: 0.2547, power: 0.2877, lr: 0.000400, took: 75.183s
[COOPERATIVE] Epoch 2, Batch 2260/3125, loss: 12.147, reward: 13.435, critic_reward: 13.715, revenue_rate: 0.3907, distance: 9.3055, memory: 0.2277, power: 0.2788, lr: 0.000400, took: 72.937s
[COOPERATIVE] Epoch 2, Batch 2270/3125, loss: 79.624, reward: 12.607, critic_reward: 13.722, revenue_rate: 0.3645, distance: 8.7104, memory: 0.2073, power: 0.2614, lr: 0.000400, took: 73.716s
[COOPERATIVE] Epoch 2, Batch 2280/3125, loss: 10.298, reward: 11.946, critic_reward: 12.644, revenue_rate: 0.3458, distance: 8.2132, memory: 0.1881, power: 0.2509, lr: 0.000400, took: 73.469s
[COOPERATIVE] Epoch 2, Batch 2290/3125, loss: -14.410, reward: 11.228, critic_reward: 11.413, revenue_rate: 0.3262, distance: 7.8471, memory: 0.1654, power: 0.2405, lr: 0.000400, took: 74.541s
[COOPERATIVE] Epoch 2, Batch 2300/3125, loss: -38.684, reward: 10.969, critic_reward: 10.773, revenue_rate: 0.3238, distance: 8.1233, memory: 0.2024, power: 0.2452, lr: 0.000400, took: 77.410s
[COOPERATIVE] Epoch 2, Batch 2310/3125, loss: -63.749, reward: 11.168, critic_reward: 10.699, revenue_rate: 0.3274, distance: 8.2072, memory: 0.1735, power: 0.2479, lr: 0.000400, took: 77.238s
[COOPERATIVE] Epoch 2, Batch 2320/3125, loss: -20.136, reward: 11.172, critic_reward: 11.091, revenue_rate: 0.3316, distance: 8.3702, memory: 0.1934, power: 0.2539, lr: 0.000400, took: 81.125s
[COOPERATIVE] Epoch 2, Batch 2330/3125, loss: -39.079, reward: 11.534, critic_reward: 11.349, revenue_rate: 0.3415, distance: 8.6019, memory: 0.2018, power: 0.2589, lr: 0.000400, took: 76.268s
[COOPERATIVE] Epoch 2, Batch 2340/3125, loss: -5.256, reward: 11.291, critic_reward: 11.469, revenue_rate: 0.3338, distance: 8.3726, memory: 0.1869, power: 0.2519, lr: 0.000400, took: 76.154s
[COOPERATIVE] Epoch 2, Batch 2350/3125, loss: -63.805, reward: 11.557, critic_reward: 11.135, revenue_rate: 0.3401, distance: 8.5098, memory: 0.2110, power: 0.2568, lr: 0.000400, took: 76.769s
[COOPERATIVE] Epoch 2, Batch 2360/3125, loss: -46.851, reward: 11.483, critic_reward: 11.220, revenue_rate: 0.3380, distance: 8.4658, memory: 0.2008, power: 0.2581, lr: 0.000400, took: 74.382s
[COOPERATIVE] Epoch 2, Batch 2370/3125, loss: -63.778, reward: 12.237, critic_reward: 11.712, revenue_rate: 0.3587, distance: 8.8097, memory: 0.2017, power: 0.2681, lr: 0.000400, took: 74.897s
[COOPERATIVE] Epoch 2, Batch 2380/3125, loss: -104.263, reward: 13.375, critic_reward: 12.492, revenue_rate: 0.3915, distance: 9.4926, memory: 0.2379, power: 0.2865, lr: 0.000400, took: 72.068s
[COOPERATIVE] Epoch 2, Batch 2390/3125, loss: -36.546, reward: 13.398, critic_reward: 13.136, revenue_rate: 0.3922, distance: 9.5047, memory: 0.2393, power: 0.2848, lr: 0.000400, took: 72.501s
[COOPERATIVE] Epoch 2, Batch 2400/3125, loss: -34.532, reward: 13.808, critic_reward: 13.617, revenue_rate: 0.4007, distance: 9.5636, memory: 0.2310, power: 0.2874, lr: 0.000400, took: 73.498s
[COOPERATIVE] Epoch 2, Batch 2410/3125, loss: 24.481, reward: 13.865, critic_reward: 14.384, revenue_rate: 0.4046, distance: 9.7096, memory: 0.2352, power: 0.2946, lr: 0.000400, took: 73.086s
[COOPERATIVE] Epoch 2, Batch 2420/3125, loss: 31.966, reward: 13.961, critic_reward: 14.507, revenue_rate: 0.4063, distance: 9.8290, memory: 0.2445, power: 0.2925, lr: 0.000400, took: 73.815s
[COOPERATIVE] Epoch 2, Batch 2430/3125, loss: -83.525, reward: 14.405, critic_reward: 13.811, revenue_rate: 0.4183, distance: 9.9740, memory: 0.2352, power: 0.3024, lr: 0.000400, took: 78.333s
[COOPERATIVE] Epoch 2, Batch 2440/3125, loss: -36.978, reward: 14.054, critic_reward: 13.910, revenue_rate: 0.4073, distance: 9.8132, memory: 0.2369, power: 0.2946, lr: 0.000400, took: 77.362s
[COOPERATIVE] Epoch 2, Batch 2450/3125, loss: 15.068, reward: 13.490, critic_reward: 13.857, revenue_rate: 0.3963, distance: 9.8045, memory: 0.2450, power: 0.2914, lr: 0.000400, took: 74.371s
[COOPERATIVE] Epoch 2, Batch 2460/3125, loss: -79.977, reward: 13.952, critic_reward: 13.598, revenue_rate: 0.4053, distance: 9.5878, memory: 0.2405, power: 0.2923, lr: 0.000400, took: 75.183s
[COOPERATIVE] Epoch 2, Batch 2470/3125, loss: -28.295, reward: 13.556, critic_reward: 13.588, revenue_rate: 0.3929, distance: 9.4105, memory: 0.2107, power: 0.2850, lr: 0.000400, took: 72.673s
[COOPERATIVE] Epoch 2, Batch 2480/3125, loss: -3.705, reward: 13.604, critic_reward: 13.693, revenue_rate: 0.3964, distance: 9.7201, memory: 0.2308, power: 0.2908, lr: 0.000400, took: 73.883s
[COOPERATIVE] Epoch 2, Batch 2490/3125, loss: -0.075, reward: 13.426, critic_reward: 13.535, revenue_rate: 0.3934, distance: 9.5822, memory: 0.2476, power: 0.2917, lr: 0.000400, took: 74.993s
[COOPERATIVE] Epoch 2, Batch 2500/3125, loss: -16.554, reward: 13.479, critic_reward: 13.430, revenue_rate: 0.3941, distance: 9.6683, memory: 0.2497, power: 0.2876, lr: 0.000400, took: 73.490s
[COOPERATIVE] Epoch 2, Batch 2510/3125, loss: -31.755, reward: 13.732, critic_reward: 13.613, revenue_rate: 0.3984, distance: 9.6243, memory: 0.2368, power: 0.2929, lr: 0.000400, took: 73.482s
[COOPERATIVE] Epoch 2, Batch 2520/3125, loss: 23.256, reward: 13.407, critic_reward: 13.887, revenue_rate: 0.3906, distance: 9.5688, memory: 0.2424, power: 0.2876, lr: 0.000400, took: 72.212s
[COOPERATIVE] Epoch 2, Batch 2530/3125, loss: -32.258, reward: 14.109, critic_reward: 13.933, revenue_rate: 0.4100, distance: 9.7670, memory: 0.2449, power: 0.2937, lr: 0.000400, took: 75.190s
[COOPERATIVE] Epoch 2, Batch 2540/3125, loss: -68.982, reward: 14.083, critic_reward: 13.651, revenue_rate: 0.4074, distance: 9.7937, memory: 0.2390, power: 0.2916, lr: 0.000400, took: 75.987s
[COOPERATIVE] Epoch 2, Batch 2550/3125, loss: -20.782, reward: 13.512, critic_reward: 13.456, revenue_rate: 0.3916, distance: 9.3770, memory: 0.2338, power: 0.2839, lr: 0.000400, took: 72.290s
[COOPERATIVE] Epoch 2, Batch 2560/3125, loss: 11.866, reward: 12.987, critic_reward: 13.270, revenue_rate: 0.3786, distance: 9.2456, memory: 0.2084, power: 0.2738, lr: 0.000400, took: 70.887s
[COOPERATIVE] Epoch 2, Batch 2570/3125, loss: -29.710, reward: 12.999, critic_reward: 12.914, revenue_rate: 0.3766, distance: 9.1809, memory: 0.2111, power: 0.2741, lr: 0.000400, took: 75.058s
[COOPERATIVE] Epoch 2, Batch 2580/3125, loss: -57.848, reward: 13.434, critic_reward: 13.007, revenue_rate: 0.3874, distance: 9.3621, memory: 0.2331, power: 0.2844, lr: 0.000400, took: 71.548s
[COOPERATIVE] Epoch 2, Batch 2590/3125, loss: -15.215, reward: 13.318, critic_reward: 13.357, revenue_rate: 0.3888, distance: 9.4746, memory: 0.2363, power: 0.2850, lr: 0.000400, took: 72.067s
[COOPERATIVE] Epoch 2, Batch 2600/3125, loss: -47.168, reward: 13.902, critic_reward: 13.632, revenue_rate: 0.4036, distance: 9.6720, memory: 0.2247, power: 0.2922, lr: 0.000400, took: 74.813s
[COOPERATIVE] Epoch 2, Batch 2610/3125, loss: 45.868, reward: 13.267, critic_reward: 14.054, revenue_rate: 0.3891, distance: 9.4999, memory: 0.2246, power: 0.2854, lr: 0.000400, took: 76.220s
[COOPERATIVE] Epoch 2, Batch 2620/3125, loss: 69.193, reward: 12.368, critic_reward: 13.489, revenue_rate: 0.3633, distance: 9.0313, memory: 0.2253, power: 0.2714, lr: 0.000400, took: 71.243s
[COOPERATIVE] Epoch 2, Batch 2630/3125, loss: -55.548, reward: 12.857, critic_reward: 12.470, revenue_rate: 0.3745, distance: 9.1591, memory: 0.2150, power: 0.2754, lr: 0.000400, took: 74.056s
[COOPERATIVE] Epoch 2, Batch 2640/3125, loss: -78.537, reward: 12.964, critic_reward: 12.339, revenue_rate: 0.3779, distance: 9.2464, memory: 0.2296, power: 0.2767, lr: 0.000400, took: 72.319s
[COOPERATIVE] Epoch 2, Batch 2650/3125, loss: -36.504, reward: 12.830, critic_reward: 12.599, revenue_rate: 0.3730, distance: 9.0889, memory: 0.2219, power: 0.2771, lr: 0.000400, took: 70.967s
[COOPERATIVE] Epoch 2, Batch 2660/3125, loss: -85.235, reward: 13.773, critic_reward: 13.199, revenue_rate: 0.3994, distance: 9.4592, memory: 0.2217, power: 0.2841, lr: 0.000400, took: 72.817s
[COOPERATIVE] Epoch 2, Batch 2670/3125, loss: 102.209, reward: 12.547, critic_reward: 13.830, revenue_rate: 0.3688, distance: 9.0244, memory: 0.2321, power: 0.2704, lr: 0.000400, took: 71.216s
[COOPERATIVE] Epoch 2, Batch 2680/3125, loss: 12.840, reward: 12.949, critic_reward: 13.410, revenue_rate: 0.3779, distance: 9.2685, memory: 0.2193, power: 0.2801, lr: 0.000400, took: 72.193s
[COOPERATIVE] Epoch 2, Batch 2690/3125, loss: 44.746, reward: 12.418, critic_reward: 13.136, revenue_rate: 0.3637, distance: 9.0242, memory: 0.2219, power: 0.2736, lr: 0.000400, took: 71.091s
[COOPERATIVE] Epoch 2, Batch 2700/3125, loss: -43.244, reward: 12.685, critic_reward: 12.443, revenue_rate: 0.3714, distance: 9.2750, memory: 0.2256, power: 0.2773, lr: 0.000400, took: 71.036s
[COOPERATIVE] Epoch 2, Batch 2710/3125, loss: -126.912, reward: 13.564, critic_reward: 12.467, revenue_rate: 0.3955, distance: 9.6689, memory: 0.2260, power: 0.2928, lr: 0.000400, took: 74.998s
[COOPERATIVE] Epoch 2, Batch 2720/3125, loss: -61.346, reward: 13.751, critic_reward: 13.298, revenue_rate: 0.4024, distance: 9.8095, memory: 0.2442, power: 0.2934, lr: 0.000400, took: 86.927s
[COOPERATIVE] Epoch 2, Batch 2730/3125, loss: 29.104, reward: 13.561, critic_reward: 13.991, revenue_rate: 0.3960, distance: 9.6654, memory: 0.2433, power: 0.2915, lr: 0.000400, took: 74.679s
[COOPERATIVE] Epoch 2, Batch 2740/3125, loss: -64.145, reward: 14.241, critic_reward: 13.905, revenue_rate: 0.4130, distance: 9.9210, memory: 0.2403, power: 0.3013, lr: 0.000400, took: 76.971s
[COOPERATIVE] Epoch 2, Batch 2750/3125, loss: -141.679, reward: 15.087, critic_reward: 13.884, revenue_rate: 0.4336, distance: 10.1636, memory: 0.2405, power: 0.3045, lr: 0.000400, took: 77.980s
[COOPERATIVE] Epoch 2, Batch 2760/3125, loss: -218.805, reward: 16.641, critic_reward: 14.701, revenue_rate: 0.4728, distance: 10.5834, memory: 0.2720, power: 0.3193, lr: 0.000400, took: 83.251s
[COOPERATIVE] Epoch 2, Batch 2770/3125, loss: 69.584, reward: 14.865, critic_reward: 15.726, revenue_rate: 0.4301, distance: 10.1383, memory: 0.2462, power: 0.3044, lr: 0.000400, took: 76.224s
[COOPERATIVE] Epoch 2, Batch 2780/3125, loss: 104.711, reward: 14.045, critic_reward: 15.371, revenue_rate: 0.4092, distance: 9.8685, memory: 0.2389, power: 0.2949, lr: 0.000400, took: 76.781s
[COOPERATIVE] Epoch 2, Batch 2790/3125, loss: 89.145, reward: 12.696, critic_reward: 14.037, revenue_rate: 0.3724, distance: 9.1820, memory: 0.2223, power: 0.2771, lr: 0.000400, took: 70.782s
[COOPERATIVE] Epoch 2, Batch 2800/3125, loss: -95.107, reward: 13.705, critic_reward: 12.914, revenue_rate: 0.3986, distance: 9.6157, memory: 0.2216, power: 0.2918, lr: 0.000400, took: 73.456s
[COOPERATIVE] Epoch 2, Batch 2810/3125, loss: -106.011, reward: 14.274, critic_reward: 13.333, revenue_rate: 0.4105, distance: 9.8672, memory: 0.2425, power: 0.2972, lr: 0.000400, took: 73.702s
[COOPERATIVE] Epoch 2, Batch 2820/3125, loss: 3.054, reward: 14.463, critic_reward: 14.665, revenue_rate: 0.4231, distance: 10.1053, memory: 0.2491, power: 0.3046, lr: 0.000400, took: 75.109s
[COOPERATIVE] Epoch 2, Batch 2830/3125, loss: 93.020, reward: 13.594, critic_reward: 14.979, revenue_rate: 0.3957, distance: 9.4837, memory: 0.2196, power: 0.2854, lr: 0.000400, took: 72.738s
[COOPERATIVE] Epoch 2, Batch 2840/3125, loss: 59.207, reward: 13.197, critic_reward: 14.066, revenue_rate: 0.3836, distance: 9.3384, memory: 0.2230, power: 0.2788, lr: 0.000400, took: 71.474s
[COOPERATIVE] Epoch 2, Batch 2850/3125, loss: -51.978, reward: 13.218, critic_reward: 12.901, revenue_rate: 0.3843, distance: 9.3533, memory: 0.2309, power: 0.2842, lr: 0.000400, took: 74.204s
[COOPERATIVE] Epoch 2, Batch 2860/3125, loss: -0.564, reward: 12.338, critic_reward: 12.520, revenue_rate: 0.3625, distance: 9.0992, memory: 0.2257, power: 0.2725, lr: 0.000400, took: 70.959s
[COOPERATIVE] Epoch 2, Batch 2870/3125, loss: -124.288, reward: 13.431, critic_reward: 12.419, revenue_rate: 0.3877, distance: 9.1674, memory: 0.2177, power: 0.2807, lr: 0.000400, took: 73.882s
[COOPERATIVE] Epoch 2, Batch 2880/3125, loss: -47.760, reward: 13.345, critic_reward: 13.087, revenue_rate: 0.3858, distance: 9.2471, memory: 0.2253, power: 0.2792, lr: 0.000400, took: 71.855s
[COOPERATIVE] Epoch 2, Batch 2890/3125, loss: 49.572, reward: 12.812, critic_reward: 13.497, revenue_rate: 0.3737, distance: 9.1407, memory: 0.2078, power: 0.2744, lr: 0.000400, took: 71.413s
[COOPERATIVE] Epoch 2, Batch 2900/3125, loss: 0.765, reward: 12.563, critic_reward: 12.843, revenue_rate: 0.3656, distance: 9.0047, memory: 0.2013, power: 0.2696, lr: 0.000400, took: 68.906s
[COOPERATIVE] Epoch 2, Batch 2910/3125, loss: 2.198, reward: 11.686, critic_reward: 12.034, revenue_rate: 0.3446, distance: 8.6703, memory: 0.2027, power: 0.2594, lr: 0.000400, took: 72.397s
[COOPERATIVE] Epoch 2, Batch 2920/3125, loss: -133.436, reward: 12.898, critic_reward: 11.732, revenue_rate: 0.3745, distance: 9.1391, memory: 0.2254, power: 0.2759, lr: 0.000400, took: 68.359s
[COOPERATIVE] Epoch 2, Batch 2930/3125, loss: -124.864, reward: 13.705, critic_reward: 12.653, revenue_rate: 0.3952, distance: 9.5010, memory: 0.2241, power: 0.2886, lr: 0.000400, took: 70.059s
[COOPERATIVE] Epoch 2, Batch 2940/3125, loss: 40.305, reward: 12.957, critic_reward: 13.592, revenue_rate: 0.3818, distance: 9.4189, memory: 0.2418, power: 0.2848, lr: 0.000400, took: 67.110s
[COOPERATIVE] Epoch 2, Batch 2950/3125, loss: 12.000, reward: 12.863, critic_reward: 13.273, revenue_rate: 0.3752, distance: 9.2049, memory: 0.2216, power: 0.2781, lr: 0.000400, took: 64.499s
[COOPERATIVE] Epoch 2, Batch 2960/3125, loss: -41.543, reward: 13.061, critic_reward: 12.816, revenue_rate: 0.3854, distance: 9.5582, memory: 0.2321, power: 0.2880, lr: 0.000400, took: 65.903s
[COOPERATIVE] Epoch 2, Batch 2970/3125, loss: -82.073, reward: 14.151, critic_reward: 13.525, revenue_rate: 0.4139, distance: 9.9509, memory: 0.2480, power: 0.2984, lr: 0.000400, took: 68.804s
[COOPERATIVE] Epoch 2, Batch 2980/3125, loss: -42.999, reward: 14.705, critic_reward: 14.588, revenue_rate: 0.4256, distance: 10.0507, memory: 0.2445, power: 0.3035, lr: 0.000400, took: 69.995s
[COOPERATIVE] Epoch 2, Batch 2990/3125, loss: 126.460, reward: 13.151, critic_reward: 14.661, revenue_rate: 0.3846, distance: 9.4058, memory: 0.2373, power: 0.2820, lr: 0.000400, took: 64.794s
[COOPERATIVE] Epoch 2, Batch 3000/3125, loss: -40.122, reward: 14.143, critic_reward: 13.868, revenue_rate: 0.4074, distance: 9.7362, memory: 0.2508, power: 0.2959, lr: 0.000400, took: 69.803s
[COOPERATIVE] Epoch 2, Batch 3010/3125, loss: -10.474, reward: 13.791, critic_reward: 13.824, revenue_rate: 0.4041, distance: 9.8067, memory: 0.2466, power: 0.2956, lr: 0.000400, took: 68.875s
[COOPERATIVE] Epoch 2, Batch 3020/3125, loss: -63.640, reward: 14.168, critic_reward: 13.778, revenue_rate: 0.4112, distance: 10.0226, memory: 0.2480, power: 0.3011, lr: 0.000400, took: 68.382s
[COOPERATIVE] Epoch 2, Batch 3030/3125, loss: -58.796, reward: 14.456, critic_reward: 14.080, revenue_rate: 0.4212, distance: 10.0839, memory: 0.2548, power: 0.3016, lr: 0.000400, took: 69.264s
[COOPERATIVE] Epoch 2, Batch 3040/3125, loss: -57.031, reward: 15.024, critic_reward: 14.619, revenue_rate: 0.4322, distance: 10.1258, memory: 0.2445, power: 0.3065, lr: 0.000400, took: 72.046s
[COOPERATIVE] Epoch 2, Batch 3050/3125, loss: -60.621, reward: 15.348, critic_reward: 14.950, revenue_rate: 0.4381, distance: 10.1889, memory: 0.2446, power: 0.3080, lr: 0.000400, took: 70.910s
[COOPERATIVE] Epoch 2, Batch 3060/3125, loss: 17.531, reward: 14.390, critic_reward: 14.893, revenue_rate: 0.4182, distance: 10.0167, memory: 0.2468, power: 0.3013, lr: 0.000400, took: 69.574s
[COOPERATIVE] Epoch 2, Batch 3070/3125, loss: 80.355, reward: 13.366, critic_reward: 14.450, revenue_rate: 0.3910, distance: 9.5832, memory: 0.2397, power: 0.2909, lr: 0.000400, took: 66.689s
[COOPERATIVE] Epoch 2, Batch 3080/3125, loss: 121.070, reward: 11.834, critic_reward: 13.592, revenue_rate: 0.3476, distance: 8.8066, memory: 0.2259, power: 0.2672, lr: 0.000400, took: 64.584s
