推理数据数量: 100
每个序列任务数量: 1250
星座卫星数量: 3
星座模式: competitive
模型: gpn+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/multi_constellation_comparison_transformer_L2H4_2025_08_13_15_04_48/constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_14_03_14_39

批次 1:
  奖励值: 131.2334
  收益率: 0.2675
  距离: 31.1311
  内存使用: 0.6050
  能量使用: 0.9969
  推理时间: 2.5435秒

批次 2:
  奖励值: 122.9797
  收益率: 0.2455
  距离: 31.4457
  内存使用: 0.8964
  能量使用: 1.0986
  推理时间: 2.4488秒

批次 3:
  奖励值: 126.1345
  收益率: 0.2488
  距离: 28.3583
  内存使用: 0.6484
  能量使用: 0.9242
  推理时间: 2.4042秒

批次 4:
  奖励值: 123.9572
  收益率: 0.2466
  距离: 29.5162
  内存使用: 0.5813
  能量使用: 0.9882
  推理时间: 2.3510秒

批次 5:
  奖励值: 126.2253
  收益率: 0.2476
  距离: 29.5748
  内存使用: 0.6959
  能量使用: 0.8786
  推理时间: 2.6056秒

批次 6:
  奖励值: 120.4912
  收益率: 0.2375
  距离: 29.1875
  内存使用: 0.5754
  能量使用: 0.8548
  推理时间: 2.3397秒

批次 7:
  奖励值: 126.1322
  收益率: 0.2523
  距离: 30.6351
  内存使用: 0.6727
  能量使用: 1.0277
  推理时间: 2.6904秒

批次 8:
  奖励值: 126.6944
  收益率: 0.2508
  距离: 28.6472
  内存使用: 0.6304
  能量使用: 0.9335
  推理时间: 2.7288秒

批次 9:
  奖励值: 118.6135
  收益率: 0.2390
  距离: 31.7654
  内存使用: 0.8991
  能量使用: 0.9086
  推理时间: 2.4656秒

批次 10:
  奖励值: 118.6996
  收益率: 0.2380
  距离: 30.1206
  内存使用: 0.8887
  能量使用: 0.9915
  推理时间: 2.4181秒

批次 11:
  奖励值: 132.7024
  收益率: 0.2681
  距离: 33.8431
  内存使用: 0.6963
  能量使用: 1.0558
  推理时间: 2.5817秒

批次 12:
  奖励值: 118.0604
  收益率: 0.2297
  距离: 28.3465
  内存使用: 0.5906
  能量使用: 0.9421
  推理时间: 2.3378秒

批次 13:
  奖励值: 117.4841
  收益率: 0.2352
  距离: 26.5747
  内存使用: 0.5670
  能量使用: 0.8900
  推理时间: 2.2819秒

批次 14:
  奖励值: 113.8456
  收益率: 0.2270
  距离: 30.1445
  内存使用: 0.6415
  能量使用: 0.9227
  推理时间: 2.1990秒

批次 15:
  奖励值: 126.9789
  收益率: 0.2573
  距离: 35.1688
  内存使用: 0.6623
  能量使用: 1.0111
  推理时间: 2.5850秒

批次 16:
  奖励值: 120.5028
  收益率: 0.2409
  距离: 27.4483
  内存使用: 0.6193
  能量使用: 0.9266
  推理时间: 2.6196秒

批次 17:
  奖励值: 128.4924
  收益率: 0.2626
  距离: 33.8087
  内存使用: 0.6202
  能量使用: 0.9863
  推理时间: 2.6727秒

批次 18:
  奖励值: 120.7541
  收益率: 0.2431
  距离: 34.9792
  内存使用: 0.6966
  能量使用: 1.0159
  推理时间: 2.6601秒

批次 19:
  奖励值: 116.8046
  收益率: 0.2260
  距离: 28.9729
  内存使用: 0.8773
  能量使用: 0.9405
  推理时间: 2.3862秒

批次 20:
  奖励值: 121.0910
  收益率: 0.2406
  距离: 31.0518
  内存使用: 0.5599
  能量使用: 1.0062
  推理时间: 2.6345秒

批次 21:
  奖励值: 125.3775
  收益率: 0.2536
  距离: 33.3168
  内存使用: 0.6946
  能量使用: 1.0337
  推理时间: 2.5164秒

批次 22:
  奖励值: 129.1142
  收益率: 0.2518
  距离: 30.4389
  内存使用: 0.6566
  能量使用: 0.8762
  推理时间: 2.6287秒

批次 23:
  奖励值: 131.1623
  收益率: 0.2642
  距离: 32.8733
  内存使用: 0.7284
  能量使用: 1.0735
  推理时间: 2.6285秒

批次 24:
  奖励值: 120.6754
  收益率: 0.2409
  距离: 31.0978
  内存使用: 0.5942
  能量使用: 0.9572
  推理时间: 2.4818秒

批次 25:
  奖励值: 112.7922
  收益率: 0.2273
  距离: 29.0093
  内存使用: 0.5822
  能量使用: 0.8240
  推理时间: 2.1983秒

批次 26:
  奖励值: 115.1345
  收益率: 0.2306
  距离: 27.8068
  内存使用: 0.6642
  能量使用: 0.8599
  推理时间: 2.4925秒

批次 27:
  奖励值: 121.5585
  收益率: 0.2439
  距离: 30.1098
  内存使用: 0.6165
  能量使用: 0.9566
  推理时间: 2.3456秒

批次 28:
  奖励值: 118.7340
  收益率: 0.2300
  距离: 27.5804
  内存使用: 0.8993
  能量使用: 0.8522
  推理时间: 2.4943秒

批次 29:
  奖励值: 115.9638
  收益率: 0.2336
  距离: 31.1663
  内存使用: 0.8908
  能量使用: 0.8153
  推理时间: 2.4760秒

批次 30:
  奖励值: 127.9199
  收益率: 0.2551
  距离: 32.4660
  内存使用: 0.6470
  能量使用: 1.0471
  推理时间: 2.9410秒

批次 31:
  奖励值: 128.8758
  收益率: 0.2612
  距离: 30.1343
  内存使用: 0.5774
  能量使用: 0.9366
  推理时间: 2.7343秒

批次 32:
  奖励值: 118.3432
  收益率: 0.2352
  距离: 28.6416
  内存使用: 0.8967
  能量使用: 0.9753
  推理时间: 2.2770秒

批次 33:
  奖励值: 119.9572
  收益率: 0.2409
  距离: 32.7448
  内存使用: 0.8286
  能量使用: 0.9122
  推理时间: 2.2684秒

批次 34:
  奖励值: 129.8776
  收益率: 0.2613
  距离: 31.0325
  内存使用: 0.6074
  能量使用: 1.0049
  推理时间: 2.5147秒

批次 35:
  奖励值: 110.0258
  收益率: 0.2191
  距离: 26.6546
  内存使用: 0.4828
  能量使用: 0.8109
  推理时间: 2.1428秒

