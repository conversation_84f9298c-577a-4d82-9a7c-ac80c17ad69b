import argparse

parser = argparse.ArgumentParser(description='Combinatorial Optimization')
# model
# pn
# gpn
parser.add_argument('--model', default='gpn')
# train
parser.add_argument('--task', default='constellation_smp')
parser.add_argument('--seed', default=12346, type=int)  # 12345
parser.add_argument('--checkpoint', default=None)
parser.add_argument('--test', action='store_true', default=False)
parser.add_argument('--max_grad_norm', default=2., type=float)
parser.add_argument('--dropout', default=0.1, type=float)
# 学习率
parser.add_argument('--actor_lr', default=4e-4, type=float)
parser.add_argument('--critic_lr', default=4e-4, type=float)
parser.add_argument('--weight_decay', type=float, default=1e-5, help='Weight decay (L2 regularization)')
parser.add_argument('--train_size', default=100000, type=int)
parser.add_argument('--valid_size', default=10000, type=int)
parser.add_argument('--epochs', default=3, type=int)
parser.add_argument('--lr', type=float, default=2e-4, help="learning rate")
parser.add_argument('--nodes', dest='num_nodes', default=100, type=int)
parser.add_argument('--hidden', dest='hidden_size', default=256, type=int)
parser.add_argument('--batch_size', default=32, type=int)
parser.add_argument('--static_size', default=9, type=int)
parser.add_argument('--dynamic_size', default=7, type=int)

parser.add_argument('--memory_total', default=0.3, type=float)
parser.add_argument('--power_total', default=5, type=float)

# 星座相关参数
parser.add_argument('--num_satellites', default=3, type=int, help='卫星星座中的卫星数量')
parser.add_argument('--constellation_mode', default='hybrid', type=str, 
                    help='星座工作模式: cooperative(协同), competitive(竞争), hybrid(混合)')
parser.add_argument('--task_sharing', action='store_true', default=True, 
                    help='是否允许卫星间共享任务信息')
parser.add_argument('--communication_delay', default=0.01, type=float, 
                    help='星座内卫星间通信延迟')
parser.add_argument('--satellite_distance', default=0.5, type=float,
                    help='卫星间平均距离（归一化）')
parser.add_argument('--verbose', action='store_true', default=True,
                    help='是否打印详细训练信息')

# MultiHead_Additive_Attention
parser.add_argument('--attention', default='MultiHead_Additive_Attention', type=str)
parser.add_argument('--n_head', default=8, type=int)

# lstm
# indrnn
# indrnnv2
parser.add_argument('--rnn', default='indrnn', type=str)
parser.add_argument('--layers', dest='num_layers', default=2, type=int)

# conv1d
parser.add_argument('--encoder', default='conv1d', type=str)

# Transformer相关参数
parser.add_argument('--use_transformer', action='store_true', default=True,
                    help='是否启用Transformer增强模块')
parser.add_argument('--transformer_layers', default=4, type=int,
                    help='Transformer编码器层数')
parser.add_argument('--transformer_heads', default=8, type=int,
                    help='Transformer多头注意力的头数')
parser.add_argument('--transformer_d_model', default=256, type=int,
                    help='Transformer模型维度，默认与hidden_size相同')
parser.add_argument('--transformer_d_ff', default=1024, type=int,
                    help='Transformer前馈网络隐藏层维度')
parser.add_argument('--transformer_dropout', default=0.1, type=float,
                    help='Transformer模块的dropout率')
parser.add_argument('--transformer_activation', default='gelu', type=str,
                    choices=['relu', 'gelu'], help='Transformer激活函数')
parser.add_argument('--transformer_max_len', default=5000, type=int,
                    help='Transformer位置编码的最大长度')
parser.add_argument('--transformer_integration_mode', default='parallel', type=str,
                    choices=['parallel', 'sequential', 'hybrid'],
                    help='Transformer集成模式: parallel(并行), sequential(串行), hybrid(混合)')

# 向后兼容性设置
parser.add_argument('--legacy_mode', action='store_true', default=False,
                    help='启用传统模式，禁用所有新功能以确保完全向后兼容')

args = parser.parse_args()

# 动态调整参数以确保兼容性
if not hasattr(args, 'transformer_d_model') or args.transformer_d_model == 256:
    args.transformer_d_model = args.hidden_size  # 与现有hidden_size保持一致

# 如果启用传统模式，禁用所有新功能
if args.legacy_mode:
    args.use_transformer = False
